{% extends '@SharedBundle/base/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('css/barcode-scanner.css') }}" rel="stylesheet">
{% endblock %}
    {% block body %}
        <!--begin::Products-->
        {%  if is_granted('ROLE_ADMIN') and data.isDbQuery %}
        <div class="align-right text-warning">Bu veri veritabanından geliyor.</div>
        {% endif %}
        <div class="card shadow-sm">
            <!--begin::Card body-->
            <div class="card-body pt-0">
                {%  include 'product/index/_table.html.twig' %}
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Products-->

        {%  include 'product/index/_barcode_scanner_modal.html.twig' %}
    {% endblock %}

{% block actions %}
    {% include 'product/index/_filter.html.twig' %}
{% endblock %}

{% block footer %}
    <!-- HTML5-QRCode kütüphanesi -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Barkod tarayıcı butonuna tıklama olayı
            const barcodeBtn = document.getElementById('barcode-scanner-btn');
            const barcodeModal = new bootstrap.Modal(document.getElementById('barcodeModal'));
            const searchInput = document.getElementById('product-search-input');

            let html5QrCode;

            // Barkod tarayıcı butonuna tıklandığında
            barcodeBtn.addEventListener('click', function() {
                barcodeModal.show();

                // Modal açıldığında QR kod tarayıcıyı başlat
                setTimeout(() => {
                    startScanner();
                }, 500);
            });

            // Modal kapandığında tarayıcıyı durdur
            document.getElementById('barcodeModal').addEventListener('hidden.bs.modal', function() {
                if (html5QrCode && html5QrCode.isScanning) {
                    html5QrCode.stop().catch(error => {
                        console.error('Tarayıcı durdurulurken hata oluştu:', error);
                    });
                }
            });

            // iOS sürümünü kontrol et
            function getiOSVersion() {
                if (/iP(hone|od|ad)/.test(navigator.platform)) {
                    const v = (navigator.appVersion).match(/OS (\d+)_(\d+)_?(\d+)?/);
                    return [parseInt(v[1], 10), parseInt(v[2], 10), parseInt(v[3] || 0, 10)];
                }
                return null;
            }

            // Tarayıcıyı başlat
            function startScanner() {
                const scanResult = document.getElementById('scanResult');
                const scannedBarcode = document.getElementById('scannedBarcode');

                // iOS sürüm kontrolü
                const iosVersion = getiOSVersion();
                const isOlderIOS = iosVersion && (iosVersion[0] < 14 || (iosVersion[0] === 14 && iosVersion[1] < 3));

                // iOS 14.3'ten önceki sürümlerde uyarı göster
                if (isOlderIOS) {
                    alert("iOS 14.3 ve üzeri sürümlerde kamera erişimi daha iyi çalışmaktadır. Sorun yaşarsanız lütfen iOS'unuzu güncelleyin.");
                }

                html5QrCode = new Html5Qrcode("reader");

                // Ekran boyutuna göre qrbox boyutunu ayarla
                const screenWidth = window.innerWidth;
                const screenHeight = window.innerHeight;

                // Mobil cihazlar için daha büyük bir tarama alanı
                let qrboxSize = { width: 250, height: 150 };

                if (screenWidth < 768) {
                    // Mobil cihazlar için
                    qrboxSize = { width: screenWidth * 0.7, height: screenWidth * 0.4 };
                } else {
                    // Tablet ve masaüstü için
                    qrboxSize = { width: 350, height: 250 };
                }

                // iOS için özel yapılandırma
                const config = {
                    fps: isOlderIOS ? 5 : 10, // iOS için daha düşük FPS
                    qrbox: qrboxSize,
                    aspectRatio: 1.777778, // 16:9 aspect ratio
                    formatsToSupport: [
                        Html5QrcodeSupportedFormats.EAN_13,
                        Html5QrcodeSupportedFormats.EAN_8,
                        Html5QrcodeSupportedFormats.CODE_39,
                        Html5QrcodeSupportedFormats.CODE_93,
                        Html5QrcodeSupportedFormats.CODE_128,
                        Html5QrcodeSupportedFormats.UPC_A,
                        Html5QrcodeSupportedFormats.UPC_E,
                        Html5QrcodeSupportedFormats.QR_CODE
                    ],
                    showTorchButtonIfSupported: true, // Flaş ışığı butonu göster (destekleniyorsa)
                    disableFlip: isOlderIOS // iOS için flip'i devre dışı bırak
                };

                // iOS Safari için kamera erişimi kontrolü
                const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
                const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

                // iOS Safari için özel mesaj
                if (isIOS && isSafari) {
                    console.log("iOS Safari tarayıcısı tespit edildi");
                }

                // Kamera erişimi için cihaz listesini al
                try {
                    // Önce kamera erişimi için izin iste - arka kamera için
                    navigator.mediaDevices.getUserMedia({
                        video: {
                            facingMode: { exact: "environment" }
                        }
                    })
                        .then(function(stream) {
                            // İzin alındı, stream'i kapat ve tarayıcıyı başlat
                            stream.getTracks().forEach(track => track.stop());

                            // Kamera listesini al
                            return navigator.mediaDevices.enumerateDevices();
                        })
                        .then(function(devices) {
                            // Arka kamera bul
                            const videoDevices = devices.filter(device => device.kind === 'videoinput');
                            let cameraId = null;

                            console.log("Bulunan kameralar:", videoDevices);

                            // Arka kamera genellikle "back" veya "environment" kelimelerini içerir
                            const backCamera = videoDevices.find(device =>
                                device.label.toLowerCase().includes('back') ||
                                device.label.toLowerCase().includes('arka') ||
                                device.label.toLowerCase().includes('environment')
                            );

                            if (backCamera) {
                                // Arka kamera bulundu
                                cameraId = backCamera.deviceId;
                                console.log("Arka kamera bulundu:", backCamera.label);
                            } else if (videoDevices.length > 1) {
                                // Birden fazla kamera var, ikincisini arka kamera olarak kabul et
                                cameraId = videoDevices[videoDevices.length - 1].deviceId;
                                console.log("İkinci kamera seçildi:", videoDevices[videoDevices.length - 1].label);
                            } else if (videoDevices.length > 0) {
                                // Tek kamera var, onu kullan
                                cameraId = videoDevices[0].deviceId;
                                console.log("Tek kamera seçildi:", videoDevices[0].label);
                            }

                            // Kamera ID'si ile başlat - her durumda environment modunu tercih et
                            // Arka kamera için exact: "environment" kullan
                            const cameraConfig = { facingMode: { exact: "environment" } };

                            // Tarayıcıyı başlat
                            return html5QrCode.start(
                                cameraConfig,
                                config,
                                (decodedText, decodedResult) => {
                                    // Barkod başarıyla tarandı
                                    console.log(`Barkod: ${decodedText}`, decodedResult);

                                    // Tarayıcıyı durdur
                                    html5QrCode.stop().then(() => {
                                        // Sonucu göster
                                        scanResult.classList.remove('d-none');
                                        scannedBarcode.textContent = decodedText;

                                        // Arama kutusuna barkodu yerleştir
                                        searchInput.value = decodedText;

                                        // Modalı kapat
                                        setTimeout(() => {
                                            barcodeModal.hide();

                                            // Arama formunu gönder
                                            searchInput.closest('form').submit();
                                        }, 1000);
                                    }).catch((err) => {
                                        console.error("Tarayıcı durdurulurken hata oluştu:", err);
                                    });
                                },
                                (errorMessage) => {
                                    // Hata durumunda
                                    console.error(errorMessage);
                                }
                            );
                        })
                        .catch(function(err) {
                            console.error("Arka kamera erişimi sırasında hata oluştu:", err);
                            console.log("Ön kamera ile deneniyor...");

                            // Arka kamera başarısız olursa, ön kamera ile dene
                            return navigator.mediaDevices.getUserMedia({
                                video: true
                            }).then(function(stream) {
                                // İzin alındı, stream'i kapat ve tarayıcıyı başlat
                                stream.getTracks().forEach(track => track.stop());

                                // Ön kamera ile başlat
                                return html5QrCode.start(
                                    { facingMode: "user" },
                                    config,
                                    (decodedText, decodedResult) => {
                                        // Barkod başarıyla tarandı
                                        console.log(`Barkod: ${decodedText}`, decodedResult);

                                        // Tarayıcıyı durdur
                                        html5QrCode.stop().then(() => {
                                            // Sonucu göster
                                            scanResult.classList.remove('d-none');
                                            scannedBarcode.textContent = decodedText;

                                            // Arama kutusuna barkodu yerleştir
                                            searchInput.value = decodedText;

                                            // Modalı kapat
                                            setTimeout(() => {
                                                barcodeModal.hide();

                                                // Arama formunu gönder
                                                searchInput.closest('form').submit();
                                            }, 1000);
                                        }).catch((err) => {
                                            console.error("Tarayıcı durdurulurken hata oluştu:", err);
                                        });
                                    },
                                    (errorMessage) => {
                                        // Hata durumunda
                                        console.error(errorMessage);
                                    }
                                );
                            }).catch(function(frontErr) {
                                console.error("Ön kamera erişimi de başarısız oldu:", frontErr);

                                // Hata mesajını göster
                                scanResult.classList.remove('d-none');
                                scanResult.classList.remove('alert-success');
                                scanResult.classList.add('alert-danger');
                                scanResult.innerHTML = "Kamera erişimi sağlanamadı. Lütfen kamera izinlerini kontrol edin.";

                                // iOS için özel mesaj
                                if (isIOS) {
                                    scanResult.innerHTML += "<br>iOS cihazlarda kamera erişimi için Safari tarayıcısını kullanmanız ve HTTPS bağlantısı gereklidir.";
                                }
                            });
                        });
                } catch (error) {
                    console.error("Kamera erişimi sırasında beklenmeyen hata:", error);

                    // Hata mesajını göster
                    scanResult.classList.remove('d-none');
                    scanResult.classList.remove('alert-success');
                    scanResult.classList.add('alert-danger');
                    scanResult.innerHTML = "Kamera erişimi sırasında beklenmeyen bir hata oluştu.";
                }
            }
        });
    </script>
{% endblock %}
