 <!--begin::Content-->
<div class="card mb-5 mb-xl-8">
    <!--begin::Header-->
    <div class="card-header border-0 pt-5 d-flex justify-content-between align-items-center">
        <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bold fs-3 mb-1">Alternatif Barkodlar</span>
            <span class="text-muted mt-1 fw-semibold fs-7">Ürüne ait alternatif barkodlar</span>
        </h3>
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#alternatifBarkodModal">
            Alternatif Barkod <PERSON>
        </button>
    </div>
    <!--end::Header-->

    <!--begin::Body-->
    <div class="card-body py-3">
        <!--begin::Table container-->
        <div class="table-responsive">
            <!--begin::Table-->
            <table class="table align-middle table-row-dashed table-row-gray-300 gy-4">
                <!--begin::Table head-->
                <thead class="bg-light">
                <tr class="fw-bold text-muted">
                    <th class="ps-4 min-w-150px rounded-start">Beden</th>
                    <th class="min-w-150px">Barkod</th>
                    <th class="min-w-200px rounded-end">Alternatif Barkod</th>
                </tr>
                </thead>
                <!--end::Table head-->

                <!--begin::Table body-->
                <tbody class="fw-semibold text-gray-600">
                {% for alt in  data.alternativeBarcodes.items %}
                <tr>
                    <td class="ps-4"><b>{{ alt.productVariant.size }}</b></td>
                    <td>{{ alt.productVariant.barcode }}</td>
                    <td>{{ alt.barcode }}</td>
                </tr>
                {% endfor %}


                </tbody>
                <!--end::Table body-->
            </table>
            <!--end::Table-->
        </div>
        <!--end::Table container-->
    </div>
    <!--end::Body-->
</div>

<!-- Modal -->
<div class="modal fade" id="alternatifBarkodModal" tabindex="-1" aria-labelledby="alternatifBarkodModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h5 class="modal-title" id="alternatifBarkodModalLabel">Alternatif Barkod Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>

            <!-- Modal Body -->
            <!-- Modal Body -->
            <div class="modal-body">
                {{ form_start(data.altBarcodeForm, {
                    'method': 'POST',
                    'action': path('app_product_edit', {'product': data.product.id})
                }) }}

                <div class="mb-3">
                    {{ form_label(data.altBarcodeForm.variant) }}
                    {{ form_widget(data.altBarcodeForm.variant, {'attr': {'class': 'form-select'}}) }}
                    {{ form_errors(data.altBarcodeForm.variant) }}
                </div>

                <div class="mb-3">
                    {{ form_label(data.altBarcodeForm.barcode) }}
                    {{ form_widget(data.altBarcodeForm.barcode, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(data.altBarcodeForm.barcode) }}
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Vazgeç</button>
                <button type="submit" class="btn btn-primary">Ekle</button>
            </div>

            {{ form_end(data.altBarcodeForm) }}


        </div>
    </div>
</div>
<!--end::Content-->