"use strict";var KTSignupComingSoon=function(){var e,t,o,n,i,r,a;return{init:function(){var s,l,u;e=document.querySelector("#kt_coming_soon_form"),t=document.querySelector("#kt_coming_soon_submit"),e&&(o=FormValidation.formValidation(e,{fields:{email:{validators:{regexp:{regexp:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"The value is not a valid email address"},notEmpty:{message:"Email address is required"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap:new FormValidation.plugins.Bootstrap5({rowSelector:".fv-row",eleInvalidClass:"",eleValidClass:""})}}),t.addEventListener("click",(function(n){n.preventDefault(),o.validate().then((function(o){"Valid"==o?(t.setAttribute("data-kt-indicator","on"),t.disabled=!0,setTimeout((function(){t.removeAttribute("data-kt-indicator"),t.disabled=!1,Swal.fire({text:"We have received your request. You will be notified once we go live.",icon:"success",buttonsStyling:!1,confirmButtonText:"Ok, got it!",customClass:{confirmButton:"btn btn-primary"}}).then((function(t){if(t.isConfirmed){e.querySelector('[name="email"]').value="";var o=e.getAttribute("data-kt-redirect-url");o&&(location.href=o)}}))}),2e3)):Swal.fire({text:"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Ok, got it!",customClass:{confirmButton:"btn btn-primary"}})}))}))),(n=document.querySelector("#kt_coming_soon_counter_days"))&&(i=document.querySelector("#kt_coming_soon_counter_hours"),r=document.querySelector("#kt_coming_soon_counter_minutes"),a=document.querySelector("#kt_coming_soon_counter_seconds"),s=new Date,l=new Date(s.getTime()+1296e6+36e6+9e5).getTime(),u=function(){var e=(new Date).getTime(),t=l-e,o=Math.floor(t/864e5),s=Math.floor(t%864e5/36e5),u=Math.floor(t%36e5/6e4),c=Math.floor(t%6e4/1e3);n&&(n.innerHTML=o),i&&(i.innerHTML=s),r&&(r.innerHTML=u),a&&(a.innerHTML=c)},setInterval(u,1e3),u())}}}();KTUtil.onDOMContentLoaded((function(){KTSignupComingSoon.init()}));