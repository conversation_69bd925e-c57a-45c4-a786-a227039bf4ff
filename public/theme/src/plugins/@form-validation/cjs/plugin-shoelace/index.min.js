/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-shoelace
 * @version 2.4.0
 */

"use strict";var t=require("@form-validation/core"),e=require("@form-validation/plugin-framework"),o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},o(t,e)};var n=t.utils.classSet,r=function(t){function e(e){return t.call(this,Object.assign({},{formClass:"fv-plugins-shoelace",messageClass:"fv-help-block",rowInvalidClass:"input-invalid",rowPattern:/^(.*)(col|offset)-[0-9]+(.*)$/,rowSelector:".input-field",rowValidClass:"input-valid"},e))||this}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}(e,t),e.prototype.onIconPlaced=function(t){var e=t.element.parentElement,o=t.element.getAttribute("type");"checkbox"!==o&&"radio"!==o||(n(t.iconElement,{"fv-plugins-icon-check":!0}),"LABEL"===e.tagName&&e.parentElement.insertBefore(t.iconElement,e.nextSibling))},e}(e.Framework);exports.Shoelace=r;
