/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/core
 * @version 2.4.0
 */

"use strict";var e={luhn:function(e){for(var t=e.length,i=[[0,1,2,3,4,5,6,7,8,9],[0,2,4,6,8,1,3,5,7,9]],r=0,n=0;t--;)n+=i[r][parseInt(e.charAt(t),10)],r=1-r;return n%10==0&&n>0},mod11And10:function(e){for(var t=e.length,i=5,r=0;r<t;r++)i=(2*(i||10)%11+parseInt(e.charAt(r),10))%10;return 1===i},mod37And36:function(e,t){void 0===t&&(t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");for(var i=e.length,r=t.length,n=Math.floor(r/2),s=0;s<i;s++)n=(2*(n||r)%(r+1)+t.indexOf(e.charAt(s)))%r;return 1===n},mod97And10:function(e){for(var t=function(e){return e.split("").map((function(e){var t=e.charCodeAt(0);return t>=65&&t<=90?t-55:e})).join("").split("").map((function(e){return parseInt(e,10)}))}(e),i=0,r=t.length,n=0;n<r-1;++n)i=10*(i+t[n])%97;return(i+=t[r-1])%97==1},verhoeff:function(e){for(var t=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],i=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],r=e.reverse(),n=0,s=0;s<r.length;s++)n=t[n][i[s%8][r[s]]];return 0===n}};var t=function(){function e(e,t){this.fields={},this.elements={},this.ee={fns:{},clear:function(){this.fns={}},emit:function(e){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];(this.fns[e]||[]).map((function(e){return e.apply(e,t)}))},off:function(e,t){if(this.fns[e]){var i=this.fns[e].indexOf(t);i>=0&&this.fns[e].splice(i,1)}},on:function(e,t){(this.fns[e]=this.fns[e]||[]).push(t)}},this.filter={filters:{},add:function(e,t){(this.filters[e]=this.filters[e]||[]).push(t)},clear:function(){this.filters={}},execute:function(e,t,i){if(!this.filters[e]||!this.filters[e].length)return t;for(var r=t,n=this.filters[e],s=n.length,l=0;l<s;l++)r=n[l].apply(r,i);return r},remove:function(e,t){this.filters[e]&&(this.filters[e]=this.filters[e].filter((function(e){return e!==t})))}},this.plugins={},this.results=new Map,this.validators={},this.form=e,this.fields=t}return e.prototype.on=function(e,t){return this.ee.on(e,t),this},e.prototype.off=function(e,t){return this.ee.off(e,t),this},e.prototype.emit=function(e){for(var t,i=[],r=1;r<arguments.length;r++)i[r-1]=arguments[r];return(t=this.ee).emit.apply(t,function(e,t,i){if(i||2===arguments.length)for(var r,n=0,s=t.length;n<s;n++)!r&&n in t||(r||(r=Array.prototype.slice.call(t,0,n)),r[n]=t[n]);return e.concat(r||Array.prototype.slice.call(t))}([e],i,!1)),this},e.prototype.registerPlugin=function(e,t){if(this.plugins[e])throw new Error("The plguin ".concat(e," is registered"));return t.setCore(this),t.install(),this.plugins[e]=t,this},e.prototype.deregisterPlugin=function(e){var t=this.plugins[e];return t&&t.uninstall(),delete this.plugins[e],this},e.prototype.enablePlugin=function(e){var t=this.plugins[e];return t&&t.enable(),this},e.prototype.disablePlugin=function(e){var t=this.plugins[e];return t&&t.disable(),this},e.prototype.isPluginEnabled=function(e){var t=this.plugins[e];return!!t&&t.isPluginEnabled()},e.prototype.registerValidator=function(e,t){if(this.validators[e])throw new Error("The validator ".concat(e," is registered"));return this.validators[e]=t,this},e.prototype.registerFilter=function(e,t){return this.filter.add(e,t),this},e.prototype.deregisterFilter=function(e,t){return this.filter.remove(e,t),this},e.prototype.executeFilter=function(e,t,i){return this.filter.execute(e,t,i)},e.prototype.addField=function(e,t){var i=Object.assign({},{selector:"",validators:{}},t);return this.fields[e]=this.fields[e]?{selector:i.selector||this.fields[e].selector,validators:Object.assign({},this.fields[e].validators,i.validators)}:i,this.elements[e]=this.queryElements(e),this.emit("core.field.added",{elements:this.elements[e],field:e,options:this.fields[e]}),this},e.prototype.removeField=function(e){if(!this.fields[e])throw new Error("The field ".concat(e," validators are not defined. Please ensure the field is added first"));var t=this.elements[e],i=this.fields[e];return delete this.elements[e],delete this.fields[e],this.emit("core.field.removed",{elements:t,field:e,options:i}),this},e.prototype.validate=function(){var e=this;return this.emit("core.form.validating",{formValidation:this}),this.filter.execute("validate-pre",Promise.resolve(),[]).then((function(){return Promise.all(Object.keys(e.fields).map((function(t){return e.validateField(t)}))).then((function(t){switch(!0){case-1!==t.indexOf("Invalid"):return e.emit("core.form.invalid",{formValidation:e}),Promise.resolve("Invalid");case-1!==t.indexOf("NotValidated"):return e.emit("core.form.notvalidated",{formValidation:e}),Promise.resolve("NotValidated");default:return e.emit("core.form.valid",{formValidation:e}),Promise.resolve("Valid")}}))}))},e.prototype.validateField=function(e){var t=this,i=this.results.get(e);if("Valid"===i||"Invalid"===i)return Promise.resolve(i);this.emit("core.field.validating",e);var r=this.elements[e];if(0===r.length)return this.emit("core.field.valid",e),Promise.resolve("Valid");var n=r[0].getAttribute("type");return"radio"===n||"checkbox"===n||1===r.length?this.validateElement(e,r[0]):Promise.all(r.map((function(i){return t.validateElement(e,i)}))).then((function(i){switch(!0){case-1!==i.indexOf("Invalid"):return t.emit("core.field.invalid",e),t.results.set(e,"Invalid"),Promise.resolve("Invalid");case-1!==i.indexOf("NotValidated"):return t.emit("core.field.notvalidated",e),t.results.delete(e),Promise.resolve("NotValidated");default:return t.emit("core.field.valid",e),t.results.set(e,"Valid"),Promise.resolve("Valid")}}))},e.prototype.validateElement=function(e,t){var i=this;this.results.delete(e);var r=this.elements[e];if(this.filter.execute("element-ignored",!1,[e,t,r]))return this.emit("core.element.ignored",{element:t,elements:r,field:e}),Promise.resolve("Ignored");var n=this.fields[e].validators;this.emit("core.element.validating",{element:t,elements:r,field:e});var s=Object.keys(n).map((function(r){return function(){return i.executeValidator(e,t,r,n[r])}}));return this.waterfall(s).then((function(n){var s=-1===n.indexOf("Invalid");i.emit("core.element.validated",{element:t,elements:r,field:e,valid:s});var l=t.getAttribute("type");return"radio"!==l&&"checkbox"!==l&&1!==r.length||i.emit(s?"core.field.valid":"core.field.invalid",e),Promise.resolve(s?"Valid":"Invalid")})).catch((function(n){return i.emit("core.element.notvalidated",{element:t,elements:r,field:e}),Promise.resolve(n)}))},e.prototype.executeValidator=function(e,t,i,r){var n=this,s=this.elements[e],l=this.filter.execute("validator-name",i,[i,e]);if(r.message=this.filter.execute("validator-message",r.message,[this.locale,e,l]),!this.validators[l]||!1===r.enabled)return this.emit("core.validator.validated",{element:t,elements:s,field:e,result:this.normalizeResult(e,l,{valid:!0}),validator:l}),Promise.resolve("Valid");var o=this.validators[l],a=this.getElementValue(e,t,l);if(!this.filter.execute("field-should-validate",!0,[e,t,a,i]))return this.emit("core.validator.notvalidated",{element:t,elements:s,field:e,validator:i}),Promise.resolve("NotValidated");this.emit("core.validator.validating",{element:t,elements:s,field:e,validator:i});var d=o().validate({element:t,elements:s,field:e,l10n:this.localization,options:r,value:a});if("function"==typeof d.then)return d.then((function(r){var l=n.normalizeResult(e,i,r);return n.emit("core.validator.validated",{element:t,elements:s,field:e,result:l,validator:i}),l.valid?"Valid":"Invalid"}));var c=this.normalizeResult(e,i,d);return this.emit("core.validator.validated",{element:t,elements:s,field:e,result:c,validator:i}),Promise.resolve(c.valid?"Valid":"Invalid")},e.prototype.getElementValue=function(e,t,i){var r=function(e,t,i,r){var n=(i.getAttribute("type")||"").toLowerCase(),s=i.tagName.toLowerCase();if("textarea"===s)return i.value;if("select"===s){var l=i,o=l.selectedIndex;return o>=0?l.options.item(o).value:""}if("input"===s){if("radio"===n||"checkbox"===n){var a=r.filter((function(e){return e.checked})).length;return 0===a?"":a+""}return i.value}return""}(this.form,0,t,this.elements[e]);return this.filter.execute("field-value",r,[r,e,t,i])},e.prototype.getElements=function(e){return this.elements[e]},e.prototype.getFields=function(){return this.fields},e.prototype.getFormElement=function(){return this.form},e.prototype.getLocale=function(){return this.locale},e.prototype.getPlugin=function(e){return this.plugins[e]},e.prototype.updateFieldStatus=function(e,t,i){var r=this,n=this.elements[e],s=n[0].getAttribute("type");if(("radio"===s||"checkbox"===s?[n[0]]:n).forEach((function(n){return r.updateElementStatus(e,n,t,i)})),i)"Invalid"===t&&(this.emit("core.field.invalid",e),this.results.set(e,"Invalid"));else switch(t){case"NotValidated":this.emit("core.field.notvalidated",e),this.results.delete(e);break;case"Validating":this.emit("core.field.validating",e),this.results.delete(e);break;case"Valid":this.emit("core.field.valid",e),this.results.set(e,"Valid");break;case"Invalid":this.emit("core.field.invalid",e),this.results.set(e,"Invalid")}return this},e.prototype.updateElementStatus=function(e,t,i,r){var n=this,s=this.elements[e],l=this.fields[e].validators,o=r?[r]:Object.keys(l);switch(i){case"NotValidated":o.forEach((function(i){return n.emit("core.validator.notvalidated",{element:t,elements:s,field:e,validator:i})})),this.emit("core.element.notvalidated",{element:t,elements:s,field:e});break;case"Validating":o.forEach((function(i){return n.emit("core.validator.validating",{element:t,elements:s,field:e,validator:i})})),this.emit("core.element.validating",{element:t,elements:s,field:e});break;case"Valid":o.forEach((function(i){return n.emit("core.validator.validated",{element:t,elements:s,field:e,result:{message:l[i].message,valid:!0},validator:i})})),this.emit("core.element.validated",{element:t,elements:s,field:e,valid:!0});break;case"Invalid":o.forEach((function(i){return n.emit("core.validator.validated",{element:t,elements:s,field:e,result:{message:l[i].message,valid:!1},validator:i})})),this.emit("core.element.validated",{element:t,elements:s,field:e,valid:!1})}return this},e.prototype.resetForm=function(e){var t=this;return Object.keys(this.fields).forEach((function(i){return t.resetField(i,e)})),this.emit("core.form.reset",{formValidation:this,reset:e}),this},e.prototype.resetField=function(e,t){if(t){var i=this.elements[e],r=i[0].getAttribute("type");i.forEach((function(e){"radio"===r||"checkbox"===r?(e.removeAttribute("selected"),e.removeAttribute("checked"),e.checked=!1):(e.setAttribute("value",""),(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&(e.value=""))}))}return this.updateFieldStatus(e,"NotValidated"),this.emit("core.field.reset",{field:e,reset:t}),this},e.prototype.revalidateField=function(e){return this.fields[e]?(this.updateFieldStatus(e,"NotValidated"),this.validateField(e)):Promise.resolve("Ignored")},e.prototype.disableValidator=function(e,t){if(!this.fields[e])return this;var i=this.elements[e];return this.toggleValidator(!1,e,t),this.emit("core.validator.disabled",{elements:i,field:e,formValidation:this,validator:t}),this},e.prototype.enableValidator=function(e,t){if(!this.fields[e])return this;var i=this.elements[e];return this.toggleValidator(!0,e,t),this.emit("core.validator.enabled",{elements:i,field:e,formValidation:this,validator:t}),this},e.prototype.updateValidatorOption=function(e,t,i,r){return this.fields[e]&&this.fields[e].validators&&this.fields[e].validators[t]&&(this.fields[e].validators[t][i]=r),this},e.prototype.setFieldOptions=function(e,t){return this.fields[e]=t,this},e.prototype.destroy=function(){var e=this;return Object.keys(this.plugins).forEach((function(t){return e.plugins[t].uninstall()})),this.ee.clear(),this.filter.clear(),this.results.clear(),this.plugins={},this},e.prototype.setLocale=function(e,t){return this.locale=e,this.localization=t,this},e.prototype.waterfall=function(e){return e.reduce((function(e,t){return e.then((function(e){return t().then((function(t){return e.push(t),e}))}))}),Promise.resolve([]))},e.prototype.queryElements=function(e){var t=this.fields[e].selector?"#"===this.fields[e].selector.charAt(0)?'[id="'.concat(this.fields[e].selector.substring(1),'"]'):this.fields[e].selector:'[name="'.concat(e.replace(/"/g,'\\"'),'"]');return[].slice.call(this.form.querySelectorAll(t))},e.prototype.normalizeResult=function(e,t,i){var r=this.fields[e].validators[t];return Object.assign({},i,{message:i.message||(r?r.message:"")||(this.localization&&this.localization[t]&&this.localization[t].default?this.localization[t].default:"")||"The field ".concat(e," is not valid")})},e.prototype.toggleValidator=function(e,t,i){var r=this,n=this.fields[t].validators;return i&&n&&n[i]?this.fields[t].validators[i].enabled=e:i||Object.keys(n).forEach((function(i){return r.fields[t].validators[i].enabled=e})),this.updateFieldStatus(t,"NotValidated",i)},e}();var i=function(){function e(e){this.opts=e,this.isEnabled=!0}return e.prototype.setCore=function(e){return this.core=e,this},e.prototype.enable=function(){return this.isEnabled=!0,this.onEnabled(),this},e.prototype.disable=function(){return this.isEnabled=!1,this.onDisabled(),this},e.prototype.isPluginEnabled=function(){return this.isEnabled},e.prototype.onEnabled=function(){},e.prototype.onDisabled=function(){},e.prototype.install=function(){},e.prototype.uninstall=function(){},e}();var r=function(e,t){var i=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.msMatchesSelector;return i?i.call(e,t):[].slice.call(e.parentElement.querySelectorAll(t)).indexOf(e)>=0},n={call:function(e,t){if("function"==typeof e)return e.apply(this,t);if("string"==typeof e){var i=e;"()"===i.substring(i.length-2)&&(i=i.substring(0,i.length-2));for(var r=i.split("."),n=r.pop(),s=window,l=0,o=r;l<o.length;l++){s=s[o[l]]}return void 0===s[n]?null:s[n].apply(this,t)}},classSet:function(e,t){var i=[],r=[];Object.keys(t).forEach((function(e){e&&(t[e]?i.push(e):r.push(e))})),r.forEach((function(t){return function(e,t){t.split(" ").forEach((function(t){e.classList?e.classList.remove(t):e.className=e.className.replace(t,"")}))}(e,t)})),i.forEach((function(t){return function(e,t){t.split(" ").forEach((function(t){e.classList?e.classList.add(t):" ".concat(e.className," ").indexOf(" ".concat(t," "))&&(e.className+=" ".concat(t))}))}(e,t)}))},closest:function(e,t){for(var i=e;i&&!r(i,t);)i=i.parentElement;return i},fetch:function(e,t){return new Promise((function(i,r){var n,s=Object.assign({},{crossDomain:!1,headers:{},method:"GET",params:{}},t),l=Object.keys(s.params).map((function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(s.params[e]))})).join("&"),o=e.indexOf("?")>-1,a="GET"===s.method?"".concat(e).concat(o?"&":"?").concat(l):e;if(s.crossDomain){var d=document.createElement("script"),c="___FormValidationFetch_".concat(Array(12).fill("").map((function(e){return Math.random().toString(36).charAt(2)})).join(""),"___");window[c]=function(e){delete window[c],i(e)},d.src="".concat(a).concat(o?"&":"?","callback=").concat(c),d.async=!0,d.addEventListener("load",(function(){d.parentNode.removeChild(d)})),d.addEventListener("error",(function(){return r})),document.head.appendChild(d)}else{var u=new XMLHttpRequest;u.open(s.method,a),u.setRequestHeader("X-Requested-With","XMLHttpRequest"),"POST"===s.method&&u.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),Object.keys(s.headers).forEach((function(e){return u.setRequestHeader(e,s.headers[e])})),u.addEventListener("load",(function(){i(JSON.parse(this.responseText))})),u.addEventListener("error",(function(){return r})),u.send((n=s.params,Object.keys(n).map((function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(n[e]))})).join("&")))}}))},format:function(e,t){var i=Array.isArray(t)?t:[t],r=e;return i.forEach((function(e){r=r.replace("%s",e)})),r},hasClass:function(e,t){return e.classList?e.classList.contains(t):new RegExp("(^| )".concat(t,"( |$)"),"gi").test(e.className)},isValidDate:function(e,t,i,r){if(isNaN(e)||isNaN(t)||isNaN(i))return!1;if(e<1e3||e>9999||t<=0||t>12)return!1;if(i<=0||i>[31,e%400==0||e%100!=0&&e%4==0?29:28,31,30,31,30,31,31,30,31,30,31][t-1])return!1;if(!0===r){var n=new Date,s=n.getFullYear(),l=n.getMonth(),o=n.getDate();return e<s||e===s&&t-1<l||e===s&&t-1===l&&i<o}return!0},removeUndefined:function(e){return e?Object.entries(e).reduce((function(e,t){var i=t[0],r=t[1];return void 0===r||(e[i]=r),e}),{}):{}}};exports.Plugin=i,exports.algorithms=e,exports.formValidation=function(e,i){var r=Object.assign({},{fields:{},locale:"en_US",plugins:{},init:function(e){}},i),n=new t(e,r.fields);return n.setLocale(r.locale,r.localization),Object.keys(r.plugins).forEach((function(e){return n.registerPlugin(e,r.plugins[e])})),r.init(n),Object.keys(r.fields).forEach((function(e){return n.addField(e,r.fields[e])})),n},exports.utils=n;
