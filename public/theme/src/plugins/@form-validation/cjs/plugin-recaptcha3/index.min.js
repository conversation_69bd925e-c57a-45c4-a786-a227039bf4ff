/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-recaptcha3
 * @version 2.4.0
 */

"use strict";var t=require("@form-validation/core"),e=function(t,o){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},e(t,o)};var o=t.utils.fetch,n=t.utils.removeUndefined,c=function(t){function c(e){var o=t.call(this,e)||this;return o.opts=Object.assign({},{minimumScore:0},n(e)),o.iconPlacedHandler=o.onIconPlaced.bind(o),o}return function(t,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function n(){this.constructor=t}e(t,o),t.prototype=null===o?Object.create(o):(n.prototype=o.prototype,new n)}(c,t),c.prototype.install=function(){var t=this;this.core.on("plugins.icon.placed",this.iconPlacedHandler);var e=void 0===window[c.LOADED_CALLBACK]?function(){}:window[c.LOADED_CALLBACK];window[c.LOADED_CALLBACK]=function(){e();var n=document.createElement("input");n.setAttribute("type","hidden"),n.setAttribute("name",c.CAPTCHA_FIELD),document.getElementById(t.opts.element).appendChild(n),t.core.addField(c.CAPTCHA_FIELD,{validators:{promise:{message:t.opts.message,promise:function(e){return new Promise((function(e,n){window.grecaptcha.execute(t.opts.siteKey,{action:t.opts.action}).then((function(r){var i;o(t.opts.backendVerificationUrl,{method:"POST",params:(i={},i[c.CAPTCHA_FIELD]=r,i)}).then((function(o){var n="true"==="".concat(o.success)&&o.score>=t.opts.minimumScore;e({message:o.message||t.opts.message,meta:o,valid:n})})).catch((function(t){n({valid:!1})}))}))}))}}}})};var n=this.getScript();if(!document.body.querySelector('script[src="'.concat(n,'"]'))){var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.defer=!0,r.src=n,document.body.appendChild(r)}},c.prototype.uninstall=function(){delete window[c.LOADED_CALLBACK],this.core.off("plugins.icon.placed",this.iconPlacedHandler);var t=this.getScript();[].slice.call(document.body.querySelectorAll('script[src="'.concat(t,'"]'))).forEach((function(t){return t.parentNode.removeChild(t)})),this.core.removeField(c.CAPTCHA_FIELD)},c.prototype.onEnabled=function(){this.core.enableValidator(c.CAPTCHA_FIELD,"promise")},c.prototype.onDisabled=function(){this.core.disableValidator(c.CAPTCHA_FIELD,"promise")},c.prototype.getScript=function(){var t=this.opts.language?"&hl=".concat(this.opts.language):"";return"https://www.google.com/recaptcha/api.js?"+"onload=".concat(c.LOADED_CALLBACK,"&render=").concat(this.opts.siteKey).concat(t)},c.prototype.onIconPlaced=function(t){t.field===c.CAPTCHA_FIELD&&(t.iconElement.style.display="none")},c.CAPTCHA_FIELD="___g-recaptcha-token___",c.LOADED_CALLBACK="___reCaptcha3Loaded___",c}(t.Plugin);exports.Recaptcha3=c;
