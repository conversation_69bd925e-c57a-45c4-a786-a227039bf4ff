/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-icon
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(e,t){"use strict";var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},n(e,t)};var i=t.utils.classSet,o=function(e){function t(t){var n=e.call(this,t)||this;return n.icons=new Map,n.opts=Object.assign({},{invalid:"fv-plugins-icon--invalid",onPlaced:function(){},onSet:function(){},valid:"fv-plugins-icon--valid",validating:"fv-plugins-icon--validating"},t),n.elementValidatingHandler=n.onElementValidating.bind(n),n.elementValidatedHandler=n.onElementValidated.bind(n),n.elementNotValidatedHandler=n.onElementNotValidated.bind(n),n.elementIgnoredHandler=n.onElementIgnored.bind(n),n.fieldAddedHandler=n.onFieldAdded.bind(n),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}(t,e),t.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler)},t.prototype.uninstall=function(){this.icons.forEach((function(e){return e.parentNode.removeChild(e)})),this.icons.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler)},t.prototype.onEnabled=function(){this.icons.forEach((function(e,t,n){i(t,{"fv-plugins-icon--enabled":!0,"fv-plugins-icon--disabled":!1})}))},t.prototype.onDisabled=function(){this.icons.forEach((function(e,t,n){i(t,{"fv-plugins-icon--enabled":!1,"fv-plugins-icon--disabled":!0})}))},t.prototype.onFieldAdded=function(e){var t=this,n=e.elements;n&&(n.forEach((function(e){var n=t.icons.get(e);n&&(n.parentNode.removeChild(n),t.icons.delete(e))})),this.prepareFieldIcon(e.field,n))},t.prototype.prepareFieldIcon=function(e,t){var n=this;if(t.length){var i=t[0].getAttribute("type");"radio"===i||"checkbox"===i?this.prepareElementIcon(e,t[0]):t.forEach((function(t){return n.prepareElementIcon(e,t)}))}},t.prototype.prepareElementIcon=function(e,t){var n=document.createElement("i");n.setAttribute("data-field",e),t.parentNode.insertBefore(n,t.nextSibling),i(n,{"fv-plugins-icon":!0,"fv-plugins-icon--enabled":this.isEnabled,"fv-plugins-icon--disabled":!this.isEnabled});var o={classes:{invalid:this.opts.invalid,valid:this.opts.valid,validating:this.opts.validating},element:t,field:e,iconElement:n};this.core.emit("plugins.icon.placed",o),this.opts.onPlaced(o),this.icons.set(t,n)},t.prototype.onElementValidating=function(e){var t,n=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!0,t)),i={element:e.element,field:e.field,iconElement:n,status:"Validating"};this.core.emit("plugins.icon.set",i),this.opts.onSet(i)},t.prototype.onElementValidated=function(e){var t,n=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!e.valid,t[this.opts.valid]=e.valid,t[this.opts.validating]=!1,t)),i={element:e.element,field:e.field,iconElement:n,status:e.valid?"Valid":"Invalid"};this.core.emit("plugins.icon.set",i),this.opts.onSet(i)},t.prototype.onElementNotValidated=function(e){var t,n=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!1,t)),i={element:e.element,field:e.field,iconElement:n,status:"NotValidated"};this.core.emit("plugins.icon.set",i),this.opts.onSet(i)},t.prototype.onElementIgnored=function(e){var t,n=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!1,t)),i={element:e.element,field:e.field,iconElement:n,status:"Ignored"};this.core.emit("plugins.icon.set",i),this.opts.onSet(i)},t.prototype.setClasses=function(e,t,n,o){var l=t.getAttribute("type"),a="radio"===l||"checkbox"===l?n[0]:t;if(this.icons.has(a)){var s=this.icons.get(a);return i(s,o),s}return null},t}(t.Plugin);e.Icon=o}));
