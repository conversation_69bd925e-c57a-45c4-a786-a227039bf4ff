/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/validator-file
 * @version 2.4.0
 */

define(["exports"],(function(e){"use strict";var t=function(e){return-1===e.indexOf(".")?e:e.split(".").slice(0,-1).join(".")};e.file=function(){return{validate:function(e){if(""===e.value)return{valid:!0};var i,n,a=e.options.extension?e.options.extension.toLowerCase().split(",").map((function(e){return e.trim()})):[],r=e.options.type?e.options.type.toLowerCase().split(",").map((function(e){return e.trim()})):[];if(window.File&&window.FileList&&window.FileReader){var o=e.element.files,s=o.length,l=0;if(e.options.maxFiles&&s>parseInt("".concat(e.options.maxFiles),10))return{meta:{error:"INVALID_MAX_FILES"},valid:!1};if(e.options.minFiles&&s<parseInt("".concat(e.options.minFiles),10))return{meta:{error:"INVALID_MIN_FILES"},valid:!1};for(var I={},p=0;p<s;p++){if(l+=o[p].size,I={ext:i=o[p].name.substr(o[p].name.lastIndexOf(".")+1),file:o[p],size:o[p].size,type:o[p].type},e.options.minSize&&o[p].size<parseInt("".concat(e.options.minSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_SIZE"},I),valid:!1};if(e.options.maxSize&&o[p].size>parseInt("".concat(e.options.maxSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_SIZE"},I),valid:!1};if(a.length>0&&-1===a.indexOf(i.toLowerCase()))return{meta:Object.assign({},{error:"INVALID_EXTENSION"},I),valid:!1};if(r.length>0&&o[p].type&&-1===r.indexOf(o[p].type.toLowerCase()))return{meta:Object.assign({},{error:"INVALID_TYPE"},I),valid:!1};if(e.options.validateFileName&&!e.options.validateFileName(t(o[p].name)))return{meta:Object.assign({},{error:"INVALID_NAME"},I),valid:!1}}if(e.options.maxTotalSize&&l>parseInt("".concat(e.options.maxTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_TOTAL_SIZE",totalSize:l},I),valid:!1};if(e.options.minTotalSize&&l<parseInt("".concat(e.options.minTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_TOTAL_SIZE",totalSize:l},I),valid:!1}}else{if(i=e.value.substr(e.value.lastIndexOf(".")+1),a.length>0&&-1===a.indexOf(i.toLowerCase()))return{meta:{error:"INVALID_EXTENSION",ext:i},valid:!1};if(n=t(e.value),e.options.validateFileName&&!e.options.validateFileName(n))return{meta:{error:"INVALID_NAME",name:n},valid:!1}}return{valid:!0}}}}}));
