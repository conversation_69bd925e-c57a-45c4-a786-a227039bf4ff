/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-recaptcha3-token
 * @version 2.4.0
 */

import{Plugin as t}from"../core/index.min.js";var e=function(t,o){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},e(t,o)};var o=function(t){function o(e){var o=t.call(this,e)||this;return o.opts=Object.assign({},{action:"submit",hiddenTokenName:"___hidden-token___"},e),o.onValidHandler=o.onFormValid.bind(o),o}return function(t,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function n(){this.constructor=t}e(t,o),t.prototype=null===o?Object.create(o):(n.prototype=o.prototype,new n)}(o,t),o.prototype.install=function(){this.core.on("core.form.valid",this.onValidHandler),this.hiddenTokenEle=document.createElement("input"),this.hiddenTokenEle.setAttribute("type","hidden"),this.core.getFormElement().appendChild(this.hiddenTokenEle);var t=void 0===window[o.LOADED_CALLBACK]?function(){}:window[o.LOADED_CALLBACK];window[o.LOADED_CALLBACK]=function(){t()};var e=this.getScript();if(!document.body.querySelector('script[src="'.concat(e,'"]'))){var n=document.createElement("script");n.type="text/javascript",n.async=!0,n.defer=!0,n.src=e,document.body.appendChild(n)}},o.prototype.uninstall=function(){delete window[o.LOADED_CALLBACK],this.core.off("core.form.valid",this.onValidHandler);var t=this.getScript();[].slice.call(document.body.querySelectorAll('script[src="'.concat(t,'"]'))).forEach((function(t){return t.parentNode.removeChild(t)})),this.core.getFormElement().removeChild(this.hiddenTokenEle)},o.prototype.onFormValid=function(){var t=this;this.isEnabled&&window.grecaptcha.execute(this.opts.siteKey,{action:this.opts.action}).then((function(e){t.hiddenTokenEle.setAttribute("name",t.opts.hiddenTokenName),t.hiddenTokenEle.value=e;var o=t.core.getFormElement();o instanceof HTMLFormElement&&o.submit()}))},o.prototype.getScript=function(){var t=this.opts.language?"&hl=".concat(this.opts.language):"";return"https://www.google.com/recaptcha/api.js?"+"onload=".concat(o.LOADED_CALLBACK,"&render=").concat(this.opts.siteKey).concat(t)},o.LOADED_CALLBACK="___reCaptcha3TokenLoaded___",o}(t);export{o as Recaptcha3Token};
