/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/validator-not-empty
 * @version 2.4.0
 */

function t(){return{validate:function(t){var i=!!t.options&&!!t.options.trim,n=t.value;return{valid:!i&&""!==n||i&&""!==n&&""!==n.trim()}}}}export{t as notEmpty};
