/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-shoelace
 * @version 2.4.0
 */

import{utils as t}from"../core/index.min.js";import{Framework as e}from"../plugin-framework/index.min.js";var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},n(t,e)};var o=t.classSet,r=function(t){function e(e){return t.call(this,Object.assign({},{formClass:"fv-plugins-shoelace",messageClass:"fv-help-block",rowInvalidClass:"input-invalid",rowPattern:/^(.*)(col|offset)-[0-9]+(.*)$/,rowSelector:".input-field",rowValidClass:"input-valid"},e))||this}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}(e,t),e.prototype.onIconPlaced=function(t){var e=t.element.parentElement,n=t.element.getAttribute("type");"checkbox"!==n&&"radio"!==n||(o(t.iconElement,{"fv-plugins-icon-check":!0}),"LABEL"===e.tagName&&e.parentElement.insertBefore(t.iconElement,e.nextSibling))},e}(e);export{r as Shoelace};
