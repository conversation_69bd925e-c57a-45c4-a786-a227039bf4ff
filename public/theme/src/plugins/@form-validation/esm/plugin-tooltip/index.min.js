/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-tooltip
 * @version 2.4.0
 */

import{utils as t,Plugin as e}from"../core/index.min.js";var o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},o(t,e)};var i=t.classSet,n=function(t){function e(e){var o=t.call(this,e)||this;return o.messages=new Map,o.opts=Object.assign({},{placement:"top",trigger:"click"},e),o.iconPlacedHandler=o.onIconPlaced.bind(o),o.validatorValidatedHandler=o.onValidatorValidated.bind(o),o.elementValidatedHandler=o.onElementValidated.bind(o),o.documentClickHandler=o.onDocumentClicked.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(e,t),e.prototype.install=function(){var t;this.tip=document.createElement("div"),i(this.tip,((t={"fv-plugins-tooltip":!0})["fv-plugins-tooltip--".concat(this.opts.placement)]=!0,t)),document.body.appendChild(this.tip),this.core.on("plugins.icon.placed",this.iconPlacedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.addEventListener("click",this.documentClickHandler)},e.prototype.uninstall=function(){this.messages.clear(),document.body.removeChild(this.tip),this.core.off("plugins.icon.placed",this.iconPlacedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.removeEventListener("click",this.documentClickHandler)},e.prototype.onIconPlaced=function(t){var e=this;if(i(t.iconElement,{"fv-plugins-tooltip-icon":!0}),"hover"===this.opts.trigger)t.iconElement.addEventListener("mouseenter",(function(o){return e.show(t.element,o)})),t.iconElement.addEventListener("mouseleave",(function(t){return e.hide()}));else t.iconElement.addEventListener("click",(function(o){return e.show(t.element,o)}))},e.prototype.onValidatorValidated=function(t){if(!t.result.valid){var e=t.elements,o=t.element.getAttribute("type"),i="radio"===o||"checkbox"===o?e[0]:t.element,n="string"==typeof t.result.message?t.result.message:t.result.message[this.core.getLocale()];this.messages.set(i,n)}},e.prototype.onElementValidated=function(t){if(t.valid){var e=t.elements,o=t.element.getAttribute("type"),i="radio"===o||"checkbox"===o?e[0]:t.element;this.messages.delete(i)}},e.prototype.onDocumentClicked=function(t){this.hide()},e.prototype.show=function(t,e){if(this.isEnabled&&(e.preventDefault(),e.stopPropagation(),this.messages.has(t))){i(this.tip,{"fv-plugins-tooltip--hide":!1}),this.tip.innerHTML='<div class="fv-plugins-tooltip__content">'.concat(this.messages.get(t),"</div>");var o=e.target.getBoundingClientRect(),n=this.tip.getBoundingClientRect(),l=n.height,a=n.width,s=0,r=0;switch(this.opts.placement){case"bottom":s=o.top+o.height,r=o.left+o.width/2-a/2;break;case"bottom-left":s=o.top+o.height,r=o.left;break;case"bottom-right":s=o.top+o.height,r=o.left+o.width-a;break;case"left":s=o.top+o.height/2-l/2,r=o.left-a;break;case"right":s=o.top+o.height/2-l/2,r=o.left+o.width;break;case"top-left":s=o.top-l,r=o.left;break;case"top-right":s=o.top-l,r=o.left+o.width-a;break;default:s=o.top-l,r=o.left+o.width/2-a/2}s+=window.scrollY||document.documentElement.scrollTop||document.body.scrollTop||0,r+=window.scrollX||document.documentElement.scrollLeft||document.body.scrollLeft||0,this.tip.setAttribute("style","top: ".concat(s,"px; left: ").concat(r,"px"))}},e.prototype.hide=function(){this.isEnabled&&i(this.tip,{"fv-plugins-tooltip--hide":!0})},e}(e);export{n as Tooltip};
