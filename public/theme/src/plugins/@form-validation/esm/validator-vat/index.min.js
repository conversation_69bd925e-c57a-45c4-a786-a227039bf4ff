/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/validator-vat
 * @version 2.4.0
 */

import{utils as t,algorithms as r}from"../core/index.min.js";var a=t.isValidDate;var e=t.isValidDate;var s=r.mod11And10;var n=r.luhn;function u(t){var r=t;if(/^(GR|EL)[0-9]{9}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{9}$/.test(r))return{meta:{},valid:!1};8===r.length&&(r="0".concat(r));for(var a=[256,128,64,32,16,8,4,2],e=0,s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*a[s];return{meta:{},valid:"".concat(e=e%11%10)===r.substr(8,1)}}var i=r.mod11And10;var c=r.luhn;var v=t.isValidDate;var f=r.mod97And10;function l(t){if(t.length<8)return{meta:{},valid:!1};var r=t;if(8===r.length&&(r="0".concat(r)),!/^[0-9]{4}[.]{0,1}[0-9]{2}[.]{0,1}[0-9]{3}$/.test(r))return{meta:{},valid:!1};if(r=r.replace(/\./g,""),0===parseInt(r,10))return{meta:{},valid:!1};for(var a=0,e=r.length,s=0;s<e-1;s++)a+=(9-s)*parseInt(r.charAt(s),10);return 10===(a%=11)&&(a=0),{meta:{},valid:"".concat(a)===r.charAt(e-1)}}var b=r.luhn;var o=t.format,d=t.removeUndefined;function m(){var t=["AR","AT","BE","BG","BR","CH","CY","CZ","DE","DK","EE","EL","ES","FI","FR","GB","GR","HR","HU","IE","IS","IT","LT","LU","LV","MT","NL","NO","PL","PT","RO","RU","RS","SE","SK","SI","VE","ZA"];return{validate:function(r){var m=r.value;if(""===m)return{valid:!0};var A=Object.assign({},{message:""},d(r.options)),h=m.substr(0,2);if(h="function"==typeof A.country?A.country.call(this):A.country,-1===t.indexOf(h))return{valid:!0};var I={meta:{},valid:!0};switch(h.toLowerCase()){case"ar":I=function(t){var r=t.replace("-","");if(/^AR[0-9]{11}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{11}$/.test(r))return{meta:{},valid:!1};for(var a=[5,4,3,2,7,6,5,4,3,2],e=0,s=0;s<10;s++)e+=parseInt(r.charAt(s),10)*a[s];return 11==(e=11-e%11)&&(e=0),{meta:{},valid:"".concat(e)===r.substr(10)}}(m);break;case"at":I=function(t){var r=t;if(/^ATU[0-9]{8}$/.test(r)&&(r=r.substr(2)),!/^U[0-9]{8}$/.test(r))return{meta:{},valid:!1};r=r.substr(1);for(var a=[1,2,1,2,1,2,1],e=0,s=0,n=0;n<7;n++)(s=parseInt(r.charAt(n),10)*a[n])>9&&(s=Math.floor(s/10)+s%10),e+=s;return 10==(e=10-(e+4)%10)&&(e=0),{meta:{},valid:"".concat(e)===r.substr(7,1)}}(m);break;case"be":I=function(t){var r=t;return/^BE[0]?[0-9]{9}$/.test(r)&&(r=r.substr(2)),/^[0]?[0-9]{9}$/.test(r)?(9===r.length&&(r="0".concat(r)),"0"===r.substr(1,1)?{meta:{},valid:!1}:{meta:{},valid:(parseInt(r.substr(0,8),10)+parseInt(r.substr(8,2),10))%97==0}):{meta:{},valid:!1}}(m);break;case"bg":I=function(t){var r=t;if(/^BG[0-9]{9,10}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{9,10}$/.test(r))return{meta:{},valid:!1};var e=0,s=0;if(9===r.length){for(s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*(s+1);if(10==(e%=11)){for(e=0,s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*(s+3);e%=11}return{meta:{},valid:"".concat(e%=10)===r.substr(8)}}return{meta:{},valid:function(t){var r=parseInt(t.substr(0,2),10)+1900,e=parseInt(t.substr(2,2),10),s=parseInt(t.substr(4,2),10);if(e>40?(r+=100,e-=40):e>20&&(r-=100,e-=20),!a(r,e,s))return!1;for(var n=[2,4,8,5,10,9,7,3,6],u=0,i=0;i<9;i++)u+=parseInt(t.charAt(i),10)*n[i];return"".concat(u=u%11%10)===t.substr(9,1)}(r)||function(t){for(var r=[21,19,17,13,11,9,7,3,1],a=0,e=0;e<9;e++)a+=parseInt(t.charAt(e),10)*r[e];return"".concat(a%=10)===t.substr(9,1)}(r)||function(t){for(var r=[4,3,2,7,6,5,4,3,2],a=0,e=0;e<9;e++)a+=parseInt(t.charAt(e),10)*r[e];return 10!=(a=11-a%11)&&(11===a&&(a=0),"".concat(a)===t.substr(9,1))}(r)}}(m);break;case"br":I=function(t){if(""===t)return{meta:{},valid:!0};var r=t.replace(/[^\d]+/g,"");if(""===r||14!==r.length)return{meta:{},valid:!1};if("00000000000000"===r||"11111111111111"===r||"22222222222222"===r||"33333333333333"===r||"44444444444444"===r||"55555555555555"===r||"66666666666666"===r||"77777777777777"===r||"88888888888888"===r||"99999999999999"===r)return{meta:{},valid:!1};var a,e=r.length-2,s=r.substring(0,e),n=r.substring(e),u=0,i=e-7;for(a=e;a>=1;a--)u+=parseInt(s.charAt(e-a),10)*i--,i<2&&(i=9);var c=u%11<2?0:11-u%11;if(c!==parseInt(n.charAt(0),10))return{meta:{},valid:!1};for(e+=1,s=r.substring(0,e),u=0,i=e-7,a=e;a>=1;a--)u+=parseInt(s.charAt(e-a),10)*i--,i<2&&(i=9);return{meta:{},valid:(c=u%11<2?0:11-u%11)===parseInt(n.charAt(1),10)}}(m);break;case"ch":I=function(t){var r=t;if(/^CHE[0-9]{9}(MWST|TVA|IVA|TPV)?$/.test(r)&&(r=r.substr(2)),!/^E[0-9]{9}(MWST|TVA|IVA|TPV)?$/.test(r))return{meta:{},valid:!1};r=r.substr(1);for(var a=[5,4,3,2,7,6,5,4],e=0,s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*a[s];return 10==(e=11-e%11)?{meta:{},valid:!1}:(11===e&&(e=0),{meta:{},valid:"".concat(e)===r.substr(8,1)})}(m);break;case"cy":I=function(t){var r=t;if(/^CY[0-5|9][0-9]{7}[A-Z]$/.test(r)&&(r=r.substr(2)),!/^[0-5|9][0-9]{7}[A-Z]$/.test(r))return{meta:{},valid:!1};if("12"===r.substr(0,2))return{meta:{},valid:!1};for(var a=0,e={0:1,1:0,2:5,3:7,4:9,5:13,6:15,7:17,8:19,9:21},s=0;s<8;s++){var n=parseInt(r.charAt(s),10);s%2==0&&(n=e["".concat(n)]),a+=n}return{meta:{},valid:"".concat("ABCDEFGHIJKLMNOPQRSTUVWXYZ"[a%26])===r.substr(8,1)}}(m);break;case"cz":I=function(t){var r=t;if(/^CZ[0-9]{8,10}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{8,10}$/.test(r))return{meta:{},valid:!1};var a=0,s=0;if(8===r.length){if("9"==="".concat(r.charAt(0)))return{meta:{},valid:!1};for(a=0,s=0;s<7;s++)a+=parseInt(r.charAt(s),10)*(8-s);return 10==(a=11-a%11)&&(a=0),11===a&&(a=1),{meta:{},valid:"".concat(a)===r.substr(7,1)}}if(9===r.length&&"6"==="".concat(r.charAt(0))){for(a=0,s=0;s<7;s++)a+=parseInt(r.charAt(s+1),10)*(8-s);return 10==(a=11-a%11)&&(a=0),11===a&&(a=1),{meta:{},valid:"".concat(a=[8,7,6,5,4,3,2,1,0,9,10][a-1])===r.substr(8,1)}}if(9===r.length||10===r.length){var n=1900+parseInt(r.substr(0,2),10),u=parseInt(r.substr(2,2),10)%50%20,i=parseInt(r.substr(4,2),10);if(9===r.length){if(n>=1980&&(n-=100),n>1953)return{meta:{},valid:!1}}else n<1954&&(n+=100);if(!e(n,u,i))return{meta:{},valid:!1};if(10===r.length){var c=parseInt(r.substr(0,9),10)%11;return n<1985&&(c%=10),{meta:{},valid:"".concat(c)===r.substr(9,1)}}return{meta:{},valid:!0}}return{meta:{},valid:!1}}(m);break;case"de":I=function(t){var r=t;return/^DE[0-9]{9}$/.test(r)&&(r=r.substr(2)),/^[1-9][0-9]{8}$/.test(r)?{meta:{},valid:s(r)}:{meta:{},valid:!1}}(m);break;case"dk":I=function(t){var r=t;if(/^DK[0-9]{8}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{8}$/.test(r))return{meta:{},valid:!1};for(var a=0,e=[2,7,6,5,4,3,2,1],s=0;s<8;s++)a+=parseInt(r.charAt(s),10)*e[s];return{meta:{},valid:a%11==0}}(m);break;case"ee":I=function(t){var r=t;if(/^EE[0-9]{9}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{9}$/.test(r))return{meta:{},valid:!1};for(var a=0,e=[3,7,1,3,7,1,3,7,1],s=0;s<9;s++)a+=parseInt(r.charAt(s),10)*e[s];return{meta:{},valid:a%10==0}}(m);break;case"el":case"gr":I=u(m);break;case"es":I=function(t){var r=t;if(/^ES[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(r)&&(r=r.substr(2)),!/^[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(r))return{meta:{},valid:!1};var a,e,s=r.charAt(0);return/^[0-9]$/.test(s)?{meta:{type:"DNI"},valid:(a=r,e=parseInt(a.substr(0,8),10),"".concat("TRWAGMYFPDXBNJZSQVHLCKE"[e%23])===a.substr(8,1))}:/^[XYZ]$/.test(s)?{meta:{type:"NIE"},valid:function(t){var r=["XYZ".indexOf(t.charAt(0)),t.substr(1)].join(""),a="TRWAGMYFPDXBNJZSQVHLCKE"[parseInt(r,10)%23];return"".concat(a)===t.substr(8,1)}(r)}:{meta:{type:"CIF"},valid:function(t){var r,a=t.charAt(0);if(-1!=="KLM".indexOf(a))return r=parseInt(t.substr(1,8),10),"".concat(r="TRWAGMYFPDXBNJZSQVHLCKE"[r%23])===t.substr(8,1);if(-1!=="ABCDEFGHJNPQRSUVW".indexOf(a)){for(var e=[2,1,2,1,2,1,2],s=0,n=0,u=0;u<7;u++)(n=parseInt(t.charAt(u+1),10)*e[u])>9&&(n=Math.floor(n/10)+n%10),s+=n;return 10==(s=10-s%10)&&(s=0),"".concat(s)===t.substr(8,1)||"JABCDEFGHI"[s]===t.substr(8,1)}return!1}(r)}}(m);break;case"fi":I=function(t){var r=t;if(/^FI[0-9]{8}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{8}$/.test(r))return{meta:{},valid:!1};for(var a=[7,9,10,5,8,4,2,1],e=0,s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*a[s];return{meta:{},valid:e%11==0}}(m);break;case"fr":I=function(t){var r=t;if(/^FR[0-9A-Z]{2}[0-9]{9}$/.test(r)&&(r=r.substr(2)),!/^[0-9A-Z]{2}[0-9]{9}$/.test(r))return{meta:{},valid:!1};if("000"!==r.substr(2,4))return{meta:{},valid:n(r.substr(2))};if(/^[0-9]{2}$/.test(r.substr(0,2)))return{meta:{},valid:r.substr(0,2)==="".concat(parseInt(r.substr(2)+"12",10)%97)};var a="0123456789ABCDEFGHJKLMNPQRSTUVWXYZ",e=void 0;return e=/^[0-9]$/.test(r.charAt(0))?24*a.indexOf(r.charAt(0))+a.indexOf(r.charAt(1))-10:34*a.indexOf(r.charAt(0))+a.indexOf(r.charAt(1))-100,{meta:{},valid:(parseInt(r.substr(2),10)+1+Math.floor(e/11))%11==e%11}}(m);break;case"gb":I=function(t){var r=t;if((/^GB[0-9]{9}$/.test(r)||/^GB[0-9]{12}$/.test(r)||/^GBGD[0-9]{3}$/.test(r)||/^GBHA[0-9]{3}$/.test(r)||/^GB(GD|HA)8888[0-9]{5}$/.test(r))&&(r=r.substr(2)),!(/^[0-9]{9}$/.test(r)||/^[0-9]{12}$/.test(r)||/^GD[0-9]{3}$/.test(r)||/^HA[0-9]{3}$/.test(r)||/^(GD|HA)8888[0-9]{5}$/.test(r)))return{meta:{},valid:!1};var a=r.length;if(5===a){var e=r.substr(0,2),s=parseInt(r.substr(2),10);return{meta:{},valid:"GD"===e&&s<500||"HA"===e&&s>=500}}if(11===a&&("GD8888"===r.substr(0,6)||"HA8888"===r.substr(0,6)))return"GD"===r.substr(0,2)&&parseInt(r.substr(6,3),10)>=500||"HA"===r.substr(0,2)&&parseInt(r.substr(6,3),10)<500?{meta:{},valid:!1}:{meta:{},valid:parseInt(r.substr(6,3),10)%97===parseInt(r.substr(9,2),10)};if(9===a||12===a){for(var n=[8,7,6,5,4,3,2,10,1],u=0,i=0;i<9;i++)u+=parseInt(r.charAt(i),10)*n[i];return u%=97,{meta:{},valid:parseInt(r.substr(0,3),10)>=100?0===u||42===u||55===u:0===u}}return{meta:{},valid:!0}}(m);break;case"hr":I=function(t){var r=t;return/^HR[0-9]{11}$/.test(r)&&(r=r.substr(2)),/^[0-9]{11}$/.test(r)?{meta:{},valid:i(r)}:{meta:{},valid:!1}}(m);break;case"hu":I=function(t){var r=t;if(/^HU[0-9]{8}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{8}$/.test(r))return{meta:{},valid:!1};for(var a=[9,7,3,1,9,7,3,1],e=0,s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*a[s];return{meta:{},valid:e%10==0}}(m);break;case"ie":I=function(t){var r=t;if(/^IE[0-9][0-9A-Z*+][0-9]{5}[A-Z]{1,2}$/.test(r)&&(r=r.substr(2)),!/^[0-9][0-9A-Z*+][0-9]{5}[A-Z]{1,2}$/.test(r))return{meta:{},valid:!1};var a=function(t){for(var r=t;r.length<7;)r="0".concat(r);for(var a="WABCDEFGHIJKLMNOPQRSTUV",e=0,s=0;s<7;s++)e+=parseInt(r.charAt(s),10)*(8-s);return e+=9*a.indexOf(r.substr(7)),a[e%23]};return/^[0-9]+$/.test(r.substr(0,7))?{meta:{},valid:r.charAt(7)===a("".concat(r.substr(0,7)).concat(r.substr(8)))}:-1!=="ABCDEFGHIJKLMNOPQRSTUVWXYZ+*".indexOf(r.charAt(1))?{meta:{},valid:r.charAt(7)===a("".concat(r.substr(2,5)).concat(r.substr(0,1)))}:{meta:{},valid:!0}}(m);break;case"is":I=function(t){var r=t;return/^IS[0-9]{5,6}$/.test(r)&&(r=r.substr(2)),{meta:{},valid:/^[0-9]{5,6}$/.test(r)}}(m);break;case"it":I=function(t){var r=t;if(/^IT[0-9]{11}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{11}$/.test(r))return{meta:{},valid:!1};if(0===parseInt(r.substr(0,7),10))return{meta:{},valid:!1};var a=parseInt(r.substr(7,3),10);return a<1||a>201&&999!==a&&888!==a?{meta:{},valid:!1}:{meta:{},valid:c(r)}}(m);break;case"lt":I=function(t){var r=t;if(/^LT([0-9]{7}1[0-9]|[0-9]{10}1[0-9])$/.test(r)&&(r=r.substr(2)),!/^([0-9]{7}1[0-9]|[0-9]{10}1[0-9])$/.test(r))return{meta:{},valid:!1};var a,e=r.length,s=0;for(a=0;a<e-1;a++)s+=parseInt(r.charAt(a),10)*(1+a%9);var n=s%11;if(10===n)for(s=0,a=0;a<e-1;a++)s+=parseInt(r.charAt(a),10)*(1+(a+2)%9);return{meta:{},valid:"".concat(n=n%11%10)===r.charAt(e-1)}}(m);break;case"lu":I=function(t){var r=t;return/^LU[0-9]{8}$/.test(r)&&(r=r.substring(2)),/^[0-9]{8}$/.test(r)?{meta:{},valid:parseInt(r.substring(0,6),10)%89===parseInt(r.substring(6,8),10)}:{meta:{},valid:!1}}(m);break;case"lv":I=function(t){var r=t;if(/^LV[0-9]{11}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{11}$/.test(r))return{meta:{},valid:!1};var a,e=parseInt(r.charAt(0),10),s=r.length,n=0,u=[];if(e>3){for(n=0,u=[9,1,4,8,3,10,2,5,7,6,1],a=0;a<s;a++)n+=parseInt(r.charAt(a),10)*u[a];return{meta:{},valid:3==(n%=11)}}var i=parseInt(r.substr(0,2),10),c=parseInt(r.substr(2,2),10),f=parseInt(r.substr(4,2),10);if(f=f+1800+100*parseInt(r.charAt(6),10),!v(f,c,i))return{meta:{},valid:!1};for(n=0,u=[10,5,8,4,2,1,6,3,7,9],a=0;a<s-1;a++)n+=parseInt(r.charAt(a),10)*u[a];return{meta:{},valid:"".concat(n=(n+1)%11%10)===r.charAt(s-1)}}(m);break;case"mt":I=function(t){var r=t;if(/^MT[0-9]{8}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{8}$/.test(r))return{meta:{},valid:!1};for(var a=[3,4,6,7,8,9,10,1],e=0,s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*a[s];return{meta:{},valid:e%37==0}}(m);break;case"nl":I=function(t){var r=t;return/^NL[0-9]{9}B[0-9]{2}$/.test(r)&&(r=r.substr(2)),/^[0-9]{9}B[0-9]{2}$/.test(r)?{meta:{},valid:l(r.substr(0,9)).valid||f("NL".concat(r))}:{meta:{},valid:!1}}(m);break;case"no":I=function(t){var r=t;if(/^NO[0-9]{9}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{9}$/.test(r))return{meta:{},valid:!1};for(var a=[3,2,7,6,5,4,3,2],e=0,s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*a[s];return 11==(e=11-e%11)&&(e=0),{meta:{},valid:"".concat(e)===r.substr(8,1)}}(m);break;case"pl":I=function(t){var r=t;if(/^PL[0-9]{10}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{10}$/.test(r))return{meta:{},valid:!1};for(var a=[6,5,7,2,3,4,5,6,7,-1],e=0,s=0;s<10;s++)e+=parseInt(r.charAt(s),10)*a[s];return{meta:{},valid:e%11==0}}(m);break;case"pt":I=function(t){var r=t;if(/^PT[0-9]{9}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{9}$/.test(r))return{meta:{},valid:!1};for(var a=[9,8,7,6,5,4,3,2],e=0,s=0;s<8;s++)e+=parseInt(r.charAt(s),10)*a[s];return(e=11-e%11)>9&&(e=0),{meta:{},valid:"".concat(e)===r.substr(8,1)}}(m);break;case"ro":I=function(t){var r=t;if(/^RO[1-9][0-9]{1,9}$/.test(r)&&(r=r.substr(2)),!/^[1-9][0-9]{1,9}$/.test(r))return{meta:{},valid:!1};for(var a=r.length,e=[7,5,3,2,1,7,5,3,2].slice(10-a),s=0,n=0;n<a-1;n++)s+=parseInt(r.charAt(n),10)*e[n];return{meta:{},valid:"".concat(s=10*s%11%10)===r.substr(a-1,1)}}(m);break;case"rs":I=function(t){var r=t;if(/^RS[0-9]{9}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{9}$/.test(r))return{meta:{},valid:!1};for(var a=10,e=0,s=0;s<8;s++)0==(e=(parseInt(r.charAt(s),10)+a)%10)&&(e=10),a=2*e%11;return{meta:{},valid:(a+parseInt(r.substr(8,1),10))%10==1}}(m);break;case"ru":I=function(t){var r=t;if(/^RU([0-9]{10}|[0-9]{12})$/.test(r)&&(r=r.substr(2)),!/^([0-9]{10}|[0-9]{12})$/.test(r))return{meta:{},valid:!1};var a=0;if(10===r.length){var e=[2,4,10,3,5,9,4,6,8,0],s=0;for(a=0;a<10;a++)s+=parseInt(r.charAt(a),10)*e[a];return(s%=11)>9&&(s%=10),{meta:{},valid:"".concat(s)===r.substr(9,1)}}if(12===r.length){var n=[7,2,4,10,3,5,9,4,6,8,0],u=[3,7,2,4,10,3,5,9,4,6,8,0],i=0,c=0;for(a=0;a<11;a++)i+=parseInt(r.charAt(a),10)*n[a],c+=parseInt(r.charAt(a),10)*u[a];return(i%=11)>9&&(i%=10),(c%=11)>9&&(c%=10),{meta:{},valid:"".concat(i)===r.substr(10,1)&&"".concat(c)===r.substr(11,1)}}return{meta:{},valid:!0}}(m);break;case"se":I=function(t){var r=t;return/^SE[0-9]{10}01$/.test(r)&&(r=r.substr(2)),/^[0-9]{10}01$/.test(r)?(r=r.substr(0,10),{meta:{},valid:b(r)}):{meta:{},valid:!1}}(m);break;case"si":I=function(t){var r=t.match(/^(SI)?([1-9][0-9]{7})$/);if(!r)return{meta:{},valid:!1};for(var a=r[1]?t.substr(2):t,e=[8,7,6,5,4,3,2],s=0,n=0;n<7;n++)s+=parseInt(a.charAt(n),10)*e[n];return 10==(s=11-s%11)&&(s=0),{meta:{},valid:"".concat(s)===a.substr(7,1)}}(m);break;case"sk":I=function(t){var r=t;return/^SK[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(r)&&(r=r.substr(2)),/^[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(r)?{meta:{},valid:parseInt(r,10)%11==0}:{meta:{},valid:!1}}(m);break;case"ve":I=function(t){var r=t;if(/^VE[VEJPG][0-9]{9}$/.test(r)&&(r=r.substr(2)),!/^[VEJPG][0-9]{9}$/.test(r))return{meta:{},valid:!1};for(var a=[3,2,7,6,5,4,3,2],e={E:8,G:20,J:12,P:16,V:4}[r.charAt(0)],s=0;s<8;s++)e+=parseInt(r.charAt(s+1),10)*a[s];return 11!=(e=11-e%11)&&10!==e||(e=0),{meta:{},valid:"".concat(e)===r.substr(9,1)}}(m);break;case"za":I=function(t){var r=t;return/^ZA4[0-9]{9}$/.test(r)&&(r=r.substr(2)),{meta:{},valid:/^4[0-9]{9}$/.test(r)}}(m)}var p=o(r.l10n&&r.l10n.vat?A.message||r.l10n.vat.country:A.message,r.l10n&&r.l10n.vat&&r.l10n.vat.countries?r.l10n.vat.countries[h.toUpperCase()]:h.toUpperCase());return Object.assign({},{message:p},I)}}}export{m as vat};
