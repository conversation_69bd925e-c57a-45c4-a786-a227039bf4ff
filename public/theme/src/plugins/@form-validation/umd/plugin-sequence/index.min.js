/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-sequence
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],t):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.Sequence=t(e.FormValidation))}(this,(function(e){"use strict";var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},t(e,i)};var i=e.utils.removeUndefined;return function(e){function l(t){var l=e.call(this,t)||this;return l.invalidFields=new Map,l.opts=Object.assign({},{enabled:!0},i(t)),l.validatorHandler=l.onValidatorValidated.bind(l),l.shouldValidateFilter=l.shouldValidate.bind(l),l.fieldAddedHandler=l.onFieldAdded.bind(l),l.elementNotValidatedHandler=l.onElementNotValidated.bind(l),l.elementValidatingHandler=l.onElementValidating.bind(l),l}return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function l(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(l.prototype=i.prototype,new l)}(l,e),l.prototype.install=function(){this.core.on("core.validator.validated",this.validatorHandler).on("core.field.added",this.fieldAddedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.validating",this.elementValidatingHandler).registerFilter("field-should-validate",this.shouldValidateFilter)},l.prototype.uninstall=function(){this.invalidFields.clear(),this.core.off("core.validator.validated",this.validatorHandler).off("core.field.added",this.fieldAddedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.validating",this.elementValidatingHandler).deregisterFilter("field-should-validate",this.shouldValidateFilter)},l.prototype.shouldValidate=function(e,t,i,l){return!this.isEnabled||!((!0===this.opts.enabled||!0===this.opts.enabled[e])&&this.invalidFields.has(t)&&!!this.invalidFields.get(t).length&&-1===this.invalidFields.get(t).indexOf(l))},l.prototype.onValidatorValidated=function(e){var t=this.invalidFields.has(e.element)?this.invalidFields.get(e.element):[],i=t.indexOf(e.validator);e.result.valid&&i>=0?t.splice(i,1):e.result.valid||-1!==i||t.push(e.validator),this.invalidFields.set(e.element,t)},l.prototype.onFieldAdded=function(e){e.elements&&this.clearInvalidFields(e.elements)},l.prototype.onElementNotValidated=function(e){this.clearInvalidFields(e.elements)},l.prototype.onElementValidating=function(e){this.clearInvalidFields(e.elements)},l.prototype.clearInvalidFields=function(e){var t=this;e.forEach((function(e){return t.invalidFields.delete(e)}))},l}(e.Plugin)}));
