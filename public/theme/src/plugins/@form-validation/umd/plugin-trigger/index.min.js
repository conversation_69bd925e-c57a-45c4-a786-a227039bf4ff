/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-trigger
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],t):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.Trigger=t(e.FormValidation))}(this,(function(e){"use strict";var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)};return function(e){function n(t){var n=e.call(this,t)||this;n.handlers=[],n.timers=new Map;var o=document.createElement("div");return n.defaultEvent="oninput"in o?"input":"keyup",n.opts=Object.assign({},{delay:0,event:n.defaultEvent,threshold:0},t),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}(n,e),n.prototype.install=function(){this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},n.prototype.uninstall=function(){this.handlers.forEach((function(e){return e.element.removeEventListener(e.event,e.handler)})),this.handlers=[],this.timers.forEach((function(e){return window.clearTimeout(e)})),this.timers.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},n.prototype.prepareHandler=function(e,t){var n=this;t.forEach((function(t){var o=[];if(n.opts.event&&!1===n.opts.event[e])o=[];else if(n.opts.event&&n.opts.event[e]&&"function"!=typeof n.opts.event[e])o=n.opts.event[e].split(" ");else if("string"==typeof n.opts.event&&n.opts.event!==n.defaultEvent)o=n.opts.event.split(" ");else{var i=t.getAttribute("type"),r=t.tagName.toLowerCase();o=["radio"===i||"checkbox"===i||"file"===i||"select"===r?"change":n.ieVersion>=10&&t.getAttribute("placeholder")?"keyup":n.defaultEvent]}o.forEach((function(o){var i=function(o){return n.handleEvent(o,e,t)};n.handlers.push({element:t,event:o,field:e,handler:i}),t.addEventListener(o,i)}))}))},n.prototype.handleEvent=function(e,t,n){var o=this;if(this.isEnabled&&this.exceedThreshold(t,n)&&this.core.executeFilter("plugins-trigger-should-validate",!0,[t,n])){var i=function(){return o.core.validateElement(t,n).then((function(i){o.core.emit("plugins.trigger.executed",{element:n,event:e,field:t})}))},r=this.opts.delay[t]||this.opts.delay;if(0===r)i();else{var l=this.timers.get(n);l&&window.clearTimeout(l),this.timers.set(n,window.setTimeout(i,1e3*r))}}},n.prototype.onFieldAdded=function(e){this.handlers.filter((function(t){return t.field===e.field})).forEach((function(e){return e.element.removeEventListener(e.event,e.handler)})),this.prepareHandler(e.field,e.elements)},n.prototype.onFieldRemoved=function(e){this.handlers.filter((function(t){return t.field===e.field&&e.elements.indexOf(t.element)>=0})).forEach((function(e){return e.element.removeEventListener(e.event,e.handler)}))},n.prototype.exceedThreshold=function(e,t){var n=0!==this.opts.threshold[e]&&0!==this.opts.threshold&&(this.opts.threshold[e]||this.opts.threshold);if(!n)return!0;var o=t.getAttribute("type");return-1!==["button","checkbox","file","hidden","image","radio","reset","submit"].indexOf(o)||this.core.getElementValue(e,t).length>=n},n}(e.Plugin)}));
