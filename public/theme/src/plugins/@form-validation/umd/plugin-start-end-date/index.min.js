/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-start-end-date
 * @version 2.4.0
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],e):((t="undefined"!=typeof globalThis?globalThis:t||self).FormValidation=t.FormValidation||{},t.FormValidation.plugins=t.FormValidation.plugins||{},t.FormValidation.plugins.StartEndDate=e(t.FormValidation))}(this,(function(t){"use strict";var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},e(t,i)};return function(t){function i(e){var i=t.call(this,e)||this;return i.fieldValidHandler=i.onFieldValid.bind(i),i.fieldInvalidHandler=i.onFieldInvalid.bind(i),i}return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function a(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(a.prototype=i.prototype,new a)}(i,t),i.prototype.install=function(){var t=this,e=this.core.getFields();this.startDateFieldOptions=e[this.opts.startDate.field],this.endDateFieldOptions=e[this.opts.endDate.field];var i=this.core.getFormElement();this.core.on("core.field.valid",this.fieldValidHandler).on("core.field.invalid",this.fieldInvalidHandler).addField(this.opts.startDate.field,{validators:{date:{format:this.opts.format,max:function(){return i.querySelector('[name="'.concat(t.opts.endDate.field,'"]')).value},message:this.opts.startDate.message}}}).addField(this.opts.endDate.field,{validators:{date:{format:this.opts.format,message:this.opts.endDate.message,min:function(){return i.querySelector('[name="'.concat(t.opts.startDate.field,'"]')).value}}}})},i.prototype.uninstall=function(){this.core.removeField(this.opts.startDate.field),this.startDateFieldOptions&&this.core.addField(this.opts.startDate.field,this.startDateFieldOptions),this.core.removeField(this.opts.endDate.field),this.endDateFieldOptions&&this.core.addField(this.opts.endDate.field,this.endDateFieldOptions),this.core.off("core.field.valid",this.fieldValidHandler).off("core.field.invalid",this.fieldInvalidHandler)},i.prototype.onEnabled=function(){this.core.enableValidator(this.opts.startDate.field,"date").enableValidator(this.opts.endDate.field,"date")},i.prototype.onDisabled=function(){this.core.disableValidator(this.opts.startDate.field,"date").disableValidator(this.opts.endDate.field,"date")},i.prototype.onFieldInvalid=function(t){switch(t){case this.opts.startDate.field:this.startDateValid=!1;break;case this.opts.endDate.field:this.endDateValid=!1}},i.prototype.onFieldValid=function(t){switch(t){case this.opts.startDate.field:this.startDateValid=!0,this.isEnabled&&!1===this.endDateValid&&this.core.revalidateField(this.opts.endDate.field);break;case this.opts.endDate.field:this.endDateValid=!0,this.isEnabled&&!1===this.startDateValid&&this.core.revalidateField(this.opts.startDate.field)}},i}(t.Plugin)}));
