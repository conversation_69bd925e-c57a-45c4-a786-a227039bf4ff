/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-mandatory-icon
 * @version 2.4.0
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],e):((t="undefined"!=typeof globalThis?globalThis:t||self).FormValidation=t.FormValidation||{},t.FormValidation.plugins=t.FormValidation.plugins||{},t.FormValidation.plugins.MandatoryIcon=e(t.FormValidation))}(this,(function(t){"use strict";var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},e(t,n)};var n=t.utils.classSet;return function(t){function i(e){var n=t.call(this,e)||this;return n.removedIcons={Invalid:"",NotValidated:"",Valid:"",Validating:""},n.icons=new Map,n.elementValidatingHandler=n.onElementValidating.bind(n),n.elementValidatedHandler=n.onElementValidated.bind(n),n.elementNotValidatedHandler=n.onElementNotValidated.bind(n),n.iconPlacedHandler=n.onIconPlaced.bind(n),n.iconSetHandler=n.onIconSet.bind(n),n}return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}(i,t),i.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("plugins.icon.placed",this.iconPlacedHandler).on("plugins.icon.set",this.iconSetHandler)},i.prototype.uninstall=function(){this.icons.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("plugins.icon.placed",this.iconPlacedHandler).off("plugins.icon.set",this.iconSetHandler)},i.prototype.onEnabled=function(){var t=this;this.icons.forEach((function(e,i,o){var a;n(i,((a={})[t.opts.icon]=!0,a))}))},i.prototype.onDisabled=function(){var t=this;this.icons.forEach((function(e,i,o){var a;n(i,((a={})[t.opts.icon]=!1,a))}))},i.prototype.onIconPlaced=function(t){var e,i=this,o=this.core.getFields()[t.field].validators,a=this.core.getElements(t.field);if(o&&o.notEmpty&&!1!==o.notEmpty.enabled&&a.length){this.icons.set(t.element,t.iconElement);for(var l=a[0].getAttribute("type"),s=l?l.toLowerCase():"",d=0,c="checkbox"===s||"radio"===s?[a[0]]:a;d<c.length;d++){var r=c[d];""===this.core.getElementValue(t.field,r)&&n(t.iconElement,((e={})[this.opts.icon]=this.isEnabled,e))}}this.iconClasses=t.classes;var p=this.opts.icon.split(" "),f={Invalid:this.iconClasses.invalid?this.iconClasses.invalid.split(" "):[],Valid:this.iconClasses.valid?this.iconClasses.valid.split(" "):[],Validating:this.iconClasses.validating?this.iconClasses.validating.split(" "):[]};Object.keys(f).forEach((function(t){for(var e=[],n=0,o=p;n<o.length;n++){var a=o[n];-1===f[t].indexOf(a)&&e.push(a)}i.removedIcons[t]=e.join(" ")}))},i.prototype.onElementValidating=function(t){this.updateIconClasses(t.element,"Validating")},i.prototype.onElementValidated=function(t){this.updateIconClasses(t.element,t.valid?"Valid":"Invalid")},i.prototype.onElementNotValidated=function(t){this.updateIconClasses(t.element,"NotValidated")},i.prototype.updateIconClasses=function(t,e){var i,o=this.icons.get(t);o&&this.iconClasses&&(this.iconClasses.valid||this.iconClasses.invalid||this.iconClasses.validating)&&n(o,((i={})[this.removedIcons[e]]=!1,i[this.opts.icon]=!1,i))},i.prototype.onIconSet=function(t){var e,i=this.icons.get(t.element);i&&("NotValidated"===t.status&&""===this.core.getElementValue(t.field,t.element)||"Ignored"===t.status)&&n(i,((e={})[this.opts.icon]=this.isEnabled,e))},i}(t.Plugin)}));
