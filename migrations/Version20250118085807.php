<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250118085807 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE korgun_parakende (id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL, scs VARCHAR(255) NOT NULL, skod VARCHAR(255) NOT NULL, rkod INT NOT NULL, bed_kod INT NOT NULL, sat_no INT NOT NULL, sat_tip VARCHAR(255) NOT NULL, sat_tar TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, location VARCHAR(255) NOT NULL, miktar NUMERIC(10, 2) NOT NULL, fiyat NUMERIC(7, 2) NOT NULL, alis_fiyati NUMERIC(7, 2) NOT NULL, kdv_oran NUMERIC(5, 2) NOT NULL, kdv_tutar NUMERIC(7, 2) NOT NULL, isk_tutar NUMERIC(7, 2) NOT NULL, ins_un VARCHAR(255) NOT NULL, ins_dt TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, upo_un VARCHAR(255) NOT NULL, up_dt TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, product_variant_id INT NOT NULL, product_id INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_DFE5B4DEA80EF684 ON korgun_parakende (product_variant_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_DFE5B4DE4584665A ON korgun_parakende (product_id)');
        $this->addSql('ALTER TABLE korgun_parakende ADD CONSTRAINT FK_DFE5B4DEA80EF684 FOREIGN KEY (product_variant_id) REFERENCES product_variant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE korgun_parakende ADD CONSTRAINT FK_DFE5B4DE4584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE korgun_parakende DROP CONSTRAINT FK_DFE5B4DEA80EF684');
        $this->addSql('ALTER TABLE korgun_parakende DROP CONSTRAINT FK_DFE5B4DE4584665A');
        $this->addSql('DROP TABLE korgun_parakende');

    }
}
