<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241014060243 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE platform_log (id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL, channel VARCHAR(20) NOT NULL, response_code INT NOT NULL, request JSON NOT NULL, response JSON NOT NULL, batch_request_id VARCHAR(255) DEFAULT NULL, level VARCHAR(10) NOT NULL, platform_id INT DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_39199AD6FFE6496F ON platform_log (platform_id)');
        $this->addSql('ALTER TABLE platform_log ADD CONSTRAINT FK_39199AD6FFE6496F FOREIGN KEY (platform_id) REFERENCES platform (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE platform_log DROP CONSTRAINT FK_39199AD6FFE6496F');
        $this->addSql('DROP TABLE platform_log');
    }
}
