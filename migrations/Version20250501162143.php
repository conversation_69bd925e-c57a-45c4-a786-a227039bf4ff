<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250501162143 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE order_cargo_delivery ADD json JSON NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_cargo_delivery DROP json_data
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE order_cargo_delivery ADD json_data TEXT NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_cargo_delivery DROP json
        SQL);
    }
}
