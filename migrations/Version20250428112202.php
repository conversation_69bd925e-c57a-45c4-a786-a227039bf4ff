<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250428112202 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE order_item_check (id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL, is_approved BOOLEAN DEFAULT false NOT NULL, description VARCHAR(255) DEFAULT NULL, item_id INT DEFAULT NULL, user_id INT DEFAULT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A5F34A96126F525E ON order_item_check (item_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A5F34A96A76ED395 ON order_item_check (user_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_item_check ADD CONSTRAINT FK_A5F34A96126F525E FOREIGN KEY (item_id) REFERENCES order_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_item_check ADD CONSTRAINT FK_A5F34A96A76ED395 FOREIGN KEY (user_id) REFERENCES users (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE order_item_check DROP CONSTRAINT FK_A5F34A96126F525E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_item_check DROP CONSTRAINT FK_A5F34A96A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE order_item_audit
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE order_item_check
        SQL);
    }
}
