<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250501164227 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE order_item_audit (id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL, type VARCHAR(10) NOT NULL, object_id VARCHAR(255) NOT NULL, discriminator VARCHAR(255) DEFAULT NULL, transaction_hash VARCHAR(40) DEFAULT NULL, diffs JSON DEFAULT NULL, blame_id VARCHAR(255) DEFAULT NULL, blame_user VARCHAR(255) DEFAULT NULL, blame_user_fqdn VARCHAR(255) DEFAULT NULL, blame_user_firewall VARCHAR(255) DEFAULT NULL, ip VARCHAR(45) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX UNIQ_1F1512DD4F5B3969 ON campaign (special_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX UNIQ_1F1512DDC54C8C93 ON campaign (type_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX UNIQ_1F1512DD682B5931 ON campaign (scope_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP CONSTRAINT FK_B2206B4FFE6496F
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP CONSTRAINT FK_B2206B4F639F774
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP CONSTRAINT campaign_platform_pkey
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP created_at
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP updated_at
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP deleted_at
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ALTER campaign_id SET NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD CONSTRAINT FK_B2206B4FFE6496F FOREIGN KEY (platform_id) REFERENCES platform (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD CONSTRAINT FK_B2206B4F639F774 FOREIGN KEY (campaign_id) REFERENCES campaign (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD PRIMARY KEY (campaign_id, platform_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_F09EEDB67EC2F574
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_bulk ALTER file_url TYPE VARCHAR(255)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_bulk ALTER file_url DROP DEFAULT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX UNIQ_F09EEDB67EC2F574 ON campaign_bulk (process_id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_1EFC1EFCA80EF684
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX UNIQ_1EFC1EFCA80EF684 ON campaign_item (product_variant_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE platform_price_rule_scope ALTER name TYPE VARCHAR(255)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE platform_price_rule_scope ALTER name DROP DEFAULT
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tool_notification ALTER telegram_channel SET NOT NULL
        SQL);

        $this->addSql(<<<'SQL'
            CREATE TABLE order_item_audit (id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL, type VARCHAR(10) NOT NULL, object_id VARCHAR(255) NOT NULL, discriminator VARCHAR(255) DEFAULT NULL, transaction_hash VARCHAR(40) DEFAULT NULL, diffs JSON DEFAULT NULL, blame_id VARCHAR(255) DEFAULT NULL, blame_user VARCHAR(255) DEFAULT NULL, blame_user_fqdn VARCHAR(255) DEFAULT NULL, blame_user_firewall VARCHAR(255) DEFAULT NULL, ip VARCHAR(45) DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA undefined
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE promo_campaign_platform_ (platform_id INT NOT NULL, campaign_id INT NOT NULL, PRIMARY KEY(campaign_id, platform_id))
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE order_item_audit
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_1EFC1EFCA80EF684
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1EFC1EFCA80EF684 ON campaign_item (product_variant_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE platform_price_rule_scope ALTER name TYPE VARCHAR(255)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE platform_price_rule_scope ALTER name SET DEFAULT 'platform'
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE tool_notification ALTER telegram_channel DROP NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX uniq_1f1512dd682b5931
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX uniq_1f1512ddc54c8c93
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX uniq_1f1512dd4f5b3969
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX uniq_1f1512dd682b5931 ON campaign (scope_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX uniq_1f1512ddc54c8c93 ON campaign (type_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX uniq_1f1512dd4f5b3969 ON campaign (special_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP CONSTRAINT fk_b2206b4f639f774
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform DROP CONSTRAINT fk_b2206b4ffe6496f
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX campaign_platform_pkey
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD created_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ALTER campaign_id DROP NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD CONSTRAINT fk_b2206b4f639f774 FOREIGN KEY (campaign_id) REFERENCES campaign (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD CONSTRAINT fk_b2206b4ffe6496f FOREIGN KEY (platform_id) REFERENCES platform (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_platform ADD PRIMARY KEY (id)
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_F09EEDB67EC2F574
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_bulk ALTER file_url TYPE VARCHAR(255)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE campaign_bulk ALTER file_url SET DEFAULT 'N/A'
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F09EEDB67EC2F574 ON campaign_bulk (process_id)
        SQL);
    }
}
