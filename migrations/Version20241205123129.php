<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241205123129 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE product_variant_alternative_barcode (id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL, barcode VARCHAR(20) NOT NULL, product_variant_id INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_57431C71A80EF684 ON product_variant_alternative_barcode (product_variant_id)');
        $this->addSql('ALTER TABLE product_variant_alternative_barcode ADD CONSTRAINT FK_57431C71A80EF684 FOREIGN KEY (product_variant_id) REFERENCES product_variant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE product_variant_alternative_barcode DROP CONSTRAINT FK_57431C71A80EF684');
        $this->addSql('DROP TABLE product_variant_alternative_barcode');
    }
}
