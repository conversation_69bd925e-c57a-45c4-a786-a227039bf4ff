<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250316123900 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE promo_campaign_item RENAME COLUMN discounted_price TO price');
        $this->addSql('ALTER TABLE promo_campaign_item ALTER price TYPE DOUBLE PRECISION');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE promo_campaign_item RENAME COLUMN price TO discounted_price');
        $this->addSql('ALTER TABLE promo_campaign_item ALTER discounted_price TYPE DOUBLE PRECISION');
    }
}
