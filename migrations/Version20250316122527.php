<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250316122527 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE promo_campaign_item (id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL, commission DOUBLE PRECISION NOT NULL, discounted_price DOUBLE PRECISION NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, campaign_platform_id INT NOT NULL, product_variant_id INT NOT NULL, campaign_id INT NOT NULL, platform_id INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('ALTER TABLE promo_campaign_item ADD CONSTRAINT FK_9DB79DFC272C7595 FOREIGN KEY (campaign_platform_id) REFERENCES promo_campaign_platform (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE promo_campaign_item ADD CONSTRAINT FK_9DB79DFCA80EF684 FOREIGN KEY (product_variant_id) REFERENCES product_variant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE promo_campaign_item ADD CONSTRAINT FK_9DB79DFCF639F774 FOREIGN KEY (campaign_id) REFERENCES promo_campaign (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE promo_campaign_item ADD CONSTRAINT FK_9DB79DFCFFE6496F FOREIGN KEY (platform_id) REFERENCES platform (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE promo_campaign_platform_item DROP CONSTRAINT fk_59b32a84272c7595');
        $this->addSql('ALTER TABLE promo_campaign_platform_item DROP CONSTRAINT fk_59b32a84a80ef684');
        $this->addSql('DROP TABLE promo_campaign_platform_item');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA undefined');
        $this->addSql('CREATE TABLE promo_campaign_platform_item (id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL, commission DOUBLE PRECISION NOT NULL, discounted_price DOUBLE PRECISION NOT NULL, campaign_platform_id INT NOT NULL, product_variant_id INT NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, updated_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, deleted_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX uniq_59b32a84a80ef684 ON promo_campaign_platform_item (product_variant_id)');
        $this->addSql('CREATE INDEX idx_59b32a84272c7595 ON promo_campaign_platform_item (campaign_platform_id)');
        $this->addSql('ALTER TABLE promo_campaign_platform_item ADD CONSTRAINT fk_59b32a84272c7595 FOREIGN KEY (campaign_platform_id) REFERENCES promo_campaign_platform (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE promo_campaign_platform_item ADD CONSTRAINT fk_59b32a84a80ef684 FOREIGN KEY (product_variant_id) REFERENCES product_variant (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE promo_campaign_item DROP CONSTRAINT FK_9DB79DFC272C7595');
        $this->addSql('ALTER TABLE promo_campaign_item DROP CONSTRAINT FK_9DB79DFCA80EF684');
        $this->addSql('ALTER TABLE promo_campaign_item DROP CONSTRAINT FK_9DB79DFCF639F774');
        $this->addSql('ALTER TABLE promo_campaign_item DROP CONSTRAINT FK_9DB79DFCFFE6496F');
        $this->addSql('DROP TABLE promo_campaign_item');
    }
}
