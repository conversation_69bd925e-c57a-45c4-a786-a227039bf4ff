<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240901204051 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_token ADD refresh_token VARCHAR(1024) NOT NULL');
        $this->addSql('ALTER TABLE user_token ADD platform VARCHAR(32) NOT NULL');
        $this->addSql('ALTER TABLE user_token RENAME COLUMN platfom TO client_ip');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE user_token ADD platfom VARCHAR(32) NOT NULL');
        $this->addSql('ALTER TABLE user_token DROP refresh_token');
        $this->addSql('ALTER TABLE user_token DROP client_ip');
        $this->addSql('ALTER TABLE user_token DROP platform');
    }
}
