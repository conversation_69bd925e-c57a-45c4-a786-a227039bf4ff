<?php

namespace App\Api\Data\Repository;

use App\Api\Data\Interface\ICacheProvider;
use App\Api\Data\Model\StockAvailability;
use App\Api\Data\Model\StockCard;
use App\Api\Data\Model\StockLocationAvailability;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Korgün Sql Server sunucusuna bağlanmak için kullanılacak sınıf
 *
 * <AUTHOR>
 */
class StockRepository extends BaseRepository
{

    private ManagerRegistry $doctrine;

    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
        $this->doctrine = $doctrine;
    }

    public function getStockAvailability($parameters) : array
    {


        $location = $parameters['location'];
        $locationQuery = $this->getLocationQuery($parameters);
        $createQuery = $this->getCreateQueryForStockAvailability();
        $priceQuery = $this->getAllPriceTypesQuery($parameters, 'getPriceTypeQueryForStockCardAvailability');
        $optionQuery = $this->getOptionSelectQueryForStockAvailability($parameters);
        $baseWhereQuery = $this->getWhereClauseForStockAvailability($parameters);

        //15.02.2018, GetStkGMik SP yerine AYON_GetStkGMik SP'si yazıldı ve kullanıldı burada! Korgun update'lerinde dikkat edilmesi gerek. FatTar sütunu yerine updDT sütunu kullanıldı yeni SP'de!

        $query = "SET NOCOUNT ON " .
                $createQuery .
                $locationQuery
                . "
                  exec dbo.GetStkGMik
                    @SKod1=".$this->quote($parameters['stockCode1']).",
                    @SKod2=".$this->quote($parameters['stockCode2']).",
                    @RKod =0,
                    @BedKod =0,
                    @Location=".$this->quote($location).",
                    @Tar_First=" . (empty($parameters['startDate']) ? 'NULL' :$this->quote($parameters['startDate'])) . ",
                    @Tar_Last=" . (empty($parameters['endDate']) ? 'NULL' :$this->quote($parameters['endDate'])) . ",
                    @OpG='k*',
                    @OpC='*',
                    @Op2='*',
                    @OzTipC=''

                  insert #tmpGetStkeTicMik
                  Select *
                  from   #tmpGetStkGMik
                  


                  select
                  st.skod                                                  as UrunKodu,
                  isnull(sg.rkod, 0)                                       as XKod,
                  rn.Tanim                                                 as XTanim,
                  isnull(rn.ozkod1, '')                                    as XOzkod1,
                  isnull(bd.bedinx, 0)                                     as YKod,
                  bd.beden                                                 as YTanim,
                  sb.barcode                                               as Barcode, " .  
                  $priceQuery . ",
                  ".$this->quote($parameters['currency'])."                   as ParaCinsi,
    
                   CAST(Sum(isnull(tt.Giren, 0)-isnull(tt.Cikan, 0)-isnull(tt.Gireniade, 0)+isnull(tt.Cikaniade, 0))
                  AS INT) as Miktar,
                  tt.Birim " .
                  $optionQuery ."
                  from   stokkart st
                         left outer join coloso cc
                                      on st.skod = cc.skod
                         left outer join s_gchar sg
                                      on sg.skod = st.skod
                                         and sg.modul = 'X'
                         left outer join korgun_parameter.dbo.p_beden_d bd
                                      on isnull(bd.bedkod, '') = isnull(st.bedkod, '')
                         left outer join korgun_parameter.dbo.P_RNK_Tip rn
                                      on isnull(rn.Renk_Kod, 0) = isnull(sg.RKod, 0)
                         inner join stokbarcode sb
                                      on sb.skod = sg.skod
                                         and isnull(sb.rkod, 0) = isnull(sg.rkod, 0)
                                         and isnull(sb.bedkod, 0) = isnull(bd.bedinx, 0) " .
                        (array_key_exists('activeBarcode', $parameters) && $parameters['activeBarcode'] == '*' ? " and sb.activ = '*' " : "") .
                        "
                         left outer join #tmpGetStkeTicMik tt
                                      on tt.skod = st.skod
                                         and isnull(tt.rkod, 0) = isnull(sg.rkod, 0)
                                         and isnull(tt.bedkod, 0) = isnull(bd.bedinx, 0)
                                         and isnull(tt.location, '') = isnull(cc.location, '')
                  where 1=1 " .
                        $baseWhereQuery . "
                         and ( exists(select locx
                                      from   @LocationX
                                      Where  Locx = cc.Location) )
                         and isNull(st.Pasif, '') = ''
                         and exists(Select top 1 1
                                    from   #tmpGetStkeTicMik tt
                                    where  tt.skod = st.skod
                                           and isnull(tt.rkod, 0) = isnull(sg.rkod, 0)
                                           and isnull(tt.bedkod, 0) = isnull(bd.bedinx, 0)
                                           and isnull(tt.location, '') = isnull(cc.location, ''))
                  group  by st.skod,
                            isnull(sg.rkod, 0),
                            rn.Tanim,
                            rn.Ozkod1,
                            isnull(bd.bedinx, 0),
                            bd.beden,
                            tt.birim,
                            sb.barcode
                  order  by st.skod,
                            isnull(sg.rkod, 0),
                            bd.beden  ";

        $availabilities = $this->GetConnection()->fetchAllAssociative($query);
        return $availabilities;
    }
    
    public function getStockCards($parameters) : array
    {
        //TODO: Using the same parameter multiple times causes "COUNT field incorrect or syntax error" exception, see https://github.com/doctrine/dbal/issues/1473
        
        $locationQuery = $this->getLocationQuery($parameters);
        $priceQuery = $this->getAllPriceTypesQuery($parameters, 'getPriceTypeQueryForStockCards');
        $baseWhereQuery = $this->getWhereClauseForStockCards($parameters);
        
        $query = "SET NOCOUNT ON " .
                  $locationQuery . "
                  SET NOCOUNT OFF

                  SELECT *
                  FROM   (SELECT st.skod AS UrunKodu,st.tanim AS UrunTanimi,st.notu AS Notu,st.sresim AS UrunResmi, " .
                  $priceQuery . ",
                                        Isnull((SELECT TOP 1 Isnull(iskonto, 0)
                                                FROM   s_satiskfiy
                                                WHERE  skod = st.skod
                                                ORDER  BY Isnull(excheck, '') DESC,
                                                          Isnull(iskonto, 0)
                                                          DESC), 0)
                                                AS iskonto_y,Isnull(
                                 (SELECT TOP 1 Isnull(iskontotut, 0)
                                  FROM   s_satiskfiy
                                  WHERE  skod = st.skod
                                  ORDER  BY Isnull(excheck, '') DESC,
                                            Isnull(
                                            iskontotut, 0) DESC), 0)
                                                             AS
                                                             iskonto_x,st.KdvOran,
                                 st.bedkod AS BedGrp,
                                                (SELECT TOP 1 tanim
                                                 FROM
                                                korgun_parameter.dbo.p_beden_m bb
                                                WHERE
                                                bb.bedkod = st.bedkod) AS BedGrpTnm,st.StokTip,(
                                 SELECT
                                        tanim
                                 FROM
                                                korgun_parameter.dbo.p_stk_tip
                                 WHERE
                                                stok_tip = st.stoktip) AS StokTipTnm,st.Birim
                                 ,st.GRUPKOD ,
                                        (SELECT tanim
                                         FROM
                                                korgun_parameter.dbo.p_stk_grp
                                        WHERE
                                                s_grp_kod = st.grupkod) AS GRUPKODTnm,st.CinsKod,
                                                (SELECT tanim
                                                 FROM
                                                korgun_parameter.dbo.p_cns_tip
                                                WHERE
                                                cins_kod = st.cinskod) AS CinsKodTnm
                                 ,st.UreticiKodu,(
                                        SELECT tanim
                                        FROM
                                                korgun_parameter.dbo.p_urt_tip
                                        WHERE
                                                uretici_kod = st.ureticikodu) AS UreticiKoduTnm
                                 ,st.UMarka
                                        ,(SELECT tanim
                                          FROM
                                                korgun_parameter.dbo.p_urt_marka
                                         WHERE
                                                uretici_kod = st.ureticikodu
                                                AND marka = st.umarka) AS UMarkaTnm,st.Reyon,(
                                 SELECT
                                        tanim
                                                                                              FROM
                                                korgun_parameter.dbo.p_reyon
                                 WHERE
                                                reyonkod = st.reyon) AS ReyonTnm,st.OzKod1,(SELECT
                                 tanim
                                                                                            FROM
                                                korgun_parameter.dbo.par_sezon
                                                                                            WHERE
                                                kod = (SELECT ozkod1
                                                       FROM   stokkart
                                                       WHERE  ozkod1 = kod
                                                              AND skod = st.skod)) AS OzKod1Tanim
                                        ,st.OzKod2,(SELECT tanim
                                                    FROM
                                                korgun_parameter.dbo.par_taban
                                                WHERE  kod = (SELECT ozkod2
                                                              FROM   stokkart
                                                              WHERE  ozkod2 = kod
                                                                     AND skod = st.skod)) AS
                                                   OzKod2Tanim,
                                        st.OzKod3,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_e
                                                WHERE  kod = (SELECT ozkod3
                                                              FROM   stokkart
                                                              WHERE  ozkod3 = kod
                                                                     AND skod = st.skod)) AS
                                                  OzKod3Tanim,
                                        st.OzKod4,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_kategori
                                                WHERE  kod = (SELECT ozkod4
                                                              FROM   stokkart
                                                              WHERE  ozkod4 = kod
                                                                     AND skod = st.skod)) AS
                                                  OzKod4Tanim,
                                        st.OzKod5,(SELECT tanim
                                                FROM   korgun_parameter.dbo.Par_SIRALAMA
                                                WHERE  kod = (SELECT ozkod5
                                                              FROM   stokkart
                                                              WHERE  ozkod5 = kod
                                                                     AND skod = st.skod)) AS
                                                  OzKod5Tanim,
                                        st.OzKod6,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_ic_materyal
                                                WHERE  kod = (SELECT ozkod6
                                                              FROM   stokkart
                                                              WHERE  ozkod6 = kod
                                                                     AND skod = st.skod)) AS
                                                  OzKod6Tanim,
                                        st.OzKod7,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_dis_materyal
                                                WHERE  kod = (SELECT ozkod7
                                                              FROM   stokkart
                                                              WHERE  ozkod7 = kod
                                                                     AND skod = st.skod)) AS
                                                  OzKod7Tanim,
                                        st.OzKod8,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_agirlik
                                                WHERE  kod = (SELECT ozkod8
                                                              FROM   stokkart
                                                              WHERE  ozkod8 = kod
                                                                     AND skod = st.skod)) AS
                                                  OzKod8Tanim,
                                        st.OzKod9,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_tarak
                                                WHERE  kod = (SELECT ozkod9
                                                              FROM   stokkart
                                                              WHERE  ozkod9 = kod
                                                                     AND skod = st.skod)) AS
                                                  OzKod9Tanim,
                                        st.OzKod10,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_no_kalibi
                                                WHERE  kod = (SELECT ozkod10
                                                              FROM   stokkart
                                                              WHERE  ozkod10 = kod
                                                                     AND skod = st.skod)) AS
                                                   OzKod10Tanim
                                        ,st.OzKod11,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_sertifika
                                                WHERE  kod = (SELECT ozkod11
                                                              FROM   stokkart
                                                              WHERE  ozkod11 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod11Tanim
                                        ,st.OzKod12,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_tabanyuk
                                                WHERE  kod = (SELECT ozkod12
                                                              FROM   stokkart
                                                              WHERE  ozkod12 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod12Tanim
                                        ,st.OzKod13,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_topukyuk
                                                WHERE  kod = (SELECT ozkod13
                                                              FROM   stokkart
                                                              WHERE  ozkod13 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod13Tanim
                                        ,st.OzKod14,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_ciftnumara
                                                WHERE  kod = (SELECT ozkod14
                                                              FROM   stokkart
                                                              WHERE  ozkod14 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod14Tanim
                                        ,st.OzKod15,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_ortopedik
                                                WHERE  kod = (SELECT ozkod15
                                                              FROM   stokkart
                                                              WHERE  ozkod15 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod15Tanim
                                        ,st.OzKod16,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_sugecirmez
                                                WHERE  kod = (SELECT ozkod16
                                                              FROM   stokkart
                                                              WHERE  ozkod16 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod16Tanim
                                        ,st.OzKod17,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_ozellikgirildi
                                                WHERE  kod = (SELECT ozkod17
                                                              FROM   stokkart
                                                              WHERE  ozkod17 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod17Tanim
                                        ,st.OzKod18,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_desen
                                                WHERE  kod = (SELECT ozkod18
                                                              FROM   stokkart
                                                              WHERE  ozkod18 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod18Tanim
                                        ,st.OzKod19,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_desi
                                                WHERE  kod = (SELECT ozkod19
                                                              FROM   stokkart
                                                              WHERE  ozkod19 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod19Tanim
                                        ,st.OzKod20,(SELECT tanim
                                                FROM   korgun_parameter.dbo.par_URETIMYERI
                                                WHERE  kod = (SELECT ozkod20
                                                              FROM   stokkart
                                                              WHERE  ozkod20 = kod
                                                                     AND skod = st.skod)) AS
                                                    OzKod20Tanim
                          FROM   stokkart st
                          WHERE  1=1 " .
                                 $baseWhereQuery .
                                 "AND EXISTS(SELECT TOP 1 1
                                            FROM   coloso cls
                                            WHERE  EXISTS(SELECT TOP 1 1
                                                          FROM   @LocationX l
                                                          WHERE  l.locx = cls.location)
                                                   AND cls.skod = st.skod)
                                 AND Isnull(st.pasif, '') = '')n
                  GROUP  BY urunkodu,uruntanimi,urunresmi,fiyat1,fiyat2,fiyat3,fiyat4,fiyat5,fiyat6,fiyat8,
                  paracinsi1,paracinsi2,paracinsi3,paracinsi4,paracinsi5,paracinsi6,paracinsi8,
                  iskonto_y,iskonto_x,kdvoran,stoktip,stoktiptnm,birim,grupkod,grupkodtnm,cinskod,
                  cinskodtnm,ureticikodu,ureticikodutnm,umarka,notu,umarkatnm,reyon,reyontnm,
                  ozkod1,ozkod1tanim,ozkod2,ozkod2tanim,ozkod3,ozkod3tanim,ozkod4,ozkod4tanim,
                  ozkod5,ozkod5tanim,ozkod6,ozkod6tanim,ozkod7,ozkod7tanim,ozkod8,ozkod8tanim,
                  ozkod9,ozkod9tanim,ozkod10,ozkod10tanim,bedgrp,bedgrptnm,ozkod11,ozkod11tanim,
                  ozkod12,ozkod12tanim,ozkod13,ozkod13tanim,ozkod14,ozkod14tanim,ozkod15,
                  ozkod15tanim,ozkod16,ozkod16tanim,ozkod17,ozkod17tanim,ozkod18,ozkod18tanim,
                  ozkod19,ozkod19tanim,ozkod20,ozkod20tanim ";


        
        $result = $this->GetConnection()->fetchAllAssociative($query);

        return $result;
    }

    public function getStockLocationAvailability($parameters) : array
    {

        if(empty($parameters['xCode'])) {
            $parameters['xCode'] = '0';
        }
        if(empty($parameters['yCode'])) {
            $parameters['yCode'] = '0';
        }
        
        $location = $parameters['location'];
        $locationQuery = $this->getLocationQuery($parameters);
        $createQuery = $this->getCreateQueryForStockAvailability();
        $baseWhereQuery = $this->getWhereClauseForStockLocationAvailability($parameters);
        
        $query = "SET NOCOUNT ON " .
                $createQuery .
                $locationQuery
                . "
                  EXEC dbo.GetStkGMik
                    @SKod1=".$this->quote($parameters['sCode']).",
                    @SKod2=".$this->quote($parameters['sCode']).",
                    @RKod =".$this->quote($parameters['xCode']).",
                    @BedKod =".$this->quote($parameters['yCode']).",
                    @Location=".$this->quote($location).",
                    @Tar_First=NULL, 
                    @Tar_Last=NULL, 
                    @OpG='*', 
                    @OpC='*', 
                    @Op2='*', 
                    @OzTipC='' 



                  SELECT 
                    st.skod                AS UrunKodu, 
                    Isnull(sg.rkod, 0)     AS XKod, 
                    rn.tanim               AS XTanim, 
                    Isnull(bd.bedinx, 0)   AS YKod, 
                    bd.beden               AS YTanim, 
                    Sum(Isnull(tt.giren, 0)-Isnull(tt.cikan, 0)-Isnull(tt.gireniade, 0)+Isnull(tt.cikaniade, 0)) 
                    + Isnull(dbo.Kg_fn_getstkcmik (st.skod, Isnull(sg.rkod, 0), 
                    Isnull(bd.bedinx, 0), tt.location, NULL, NULL, tt.birim, '*', 'k'), 0) AS Miktar, 
                    tt.birim, 
                    tt.location, 
                    (SELECT ll.lname FROM   location ll WHERE  ll.location = tt.location) AS Location_Tanim 
                  FROM   stokkart st 
                         LEFT OUTER JOIN coloso cc 
                                      ON st.skod = cc.skod 
                         LEFT OUTER JOIN s_gchar sg 
                                      ON sg.skod = st.skod 
                                         AND sg.modul = 'X' 
                         LEFT OUTER JOIN korgun_parameter.dbo.p_beden_d bd 
                                      ON bd.bedkod = st.bedkod 
                         LEFT OUTER JOIN korgun_parameter.dbo.p_rnk_tip rn 
                                      ON rn.renk_kod = Isnull(sg.rkod, 0) 
                         LEFT OUTER JOIN #tmpgetstkgmik tt 
                                      ON tt.skod = st.skod 
                                         AND Isnull(tt.rkod, 0) = Isnull(sg.rkod, 0) 
                                         AND tt.bedkod = Isnull(bd.bedinx, 0) 
                                         AND tt.location = cc.location 
                  WHERE  1=1 " .
                        $baseWhereQuery . "
                         AND ( EXISTS(SELECT locx 
                                      FROM   @LocationX 
                                      WHERE  locx = cc.location) ) 
                         AND Isnull(st.pasif, '') = '' 
                  GROUP  BY st.skod, 
                            Isnull(sg.rkod, 0), 
                            rn.tanim, 
                            Isnull(bd.bedinx, 0), 
                            bd.beden, 
                            tt.birim, 
                            tt.location 
                  ORDER  BY st.skod, 
                            Isnull(sg.rkod, 0), 
                            bd.beden
                ";


        return $this->GetConnection()->fetchAllAssociative($query);

    }
    
    private function getLocationQuery($parameters)
    {
        $locationQuery = "  DECLARE @LocationX TABLE
                            (
                                 locx NVARCHAR(50)
                            )";
        
        if(array_key_exists('location', $parameters) && !empty($parameters['location'])) {
            $locationQuery .= " DECLARE @location NvarChar(2000) SET @location = " . $this->quote($parameters['location']) . "
                                While Charindex(',', @Location) > 0
                                Begin
                                    INSERT @LocationX
                                           (Locx)
                                    VALUES (Substring(@Location, 1, Charindex(',', @Location) - 1) )

                                    SET @Location = Substring(@Location, Charindex(',', @Location) + 1, Len(@Location))
                                end  
                                INSERT @LocationX (Locx) VALUES (@Location) ";
        }
        else {
//            if(array_key_exists('location', $parameters)){
//                unset($parameters['location']);
//            }
            
            $locationQuery .= " INSERT @LocationX
                                SELECT accesslocation
                                FROM   korgun_parameter.dbo.korgun_user_locationlist
                                WHERE  ( username = User_name() )
                                       AND ( catalogname = Db_name() )";
        }
        
        return $locationQuery;
    }

    private function getAllPriceTypesQuery($parameters, $function)
    {
        $priceQuery = [];
        for($i = 1; $i <= 8; $i++) {
            if ($i != 7){
                $priceQuery[] = $this->{$function}($parameters, $i);
            }
        }

        return implode(',', $priceQuery);
    }
    
    private function getPriceTypeQueryForStockCards($parameters, $index)
    {
        $priceType = $parameters['priceType'.$index];
        $currency = $parameters['currency'];
        $endDate = $parameters['endDate'];
        
        if($priceType == 'ACTFY') {
            $query = "ISNULL((select top 1 sf.Fiyat from S_SatFiy sf Where sf.SKOD=st.SKOD and sf.RKOD=0 and sf.BedKod=0 and sf.exCheck='*'),0) as Fiyat".$index." , ISNULL((select top 1 sf.ParaCinsi from S_SatFiy sf Where sf.SKOD=st.SKOD and sf.RKOD=0 and sf.BedKod=0 and sf.exCheck='*'),0)  as ParaCinsi" . $index;
        }
        else if($priceType != '' && $currency == 'X') {
            $query = "ISNULL((select top 1 sf.Fiyat from S_SatFiy sf Where sf.SKOD=st.SKOD and sf.RKOD=0 and sf.BedKod=0 and sf.Tip='" . $priceType . "'),0) as Fiyat".$index."  , ISNULL((select top 1 sf.ParaCinsi from S_SatFiy sf Where sf.SKOD=st.SKOD and sf.RKOD=0 and sf.BedKod=0 and sf.Tip=" . $this->quote($priceType) . "),0)  as ParaCinsi" . $index;
        }
        else if($priceType != '') {
            $query = "dbo.kg_fn_GetAlSatFiy(st.Skod, 0, 0, substring(" .$this->quote($priceType) . ",1,3), 's', " .$this->quote($endDate) . ", " .$this->quote($currency) . ", '', 'S') as Fiyat".$index.", " .$this->quote($currency) . " as ParaCinsi".$index;
        }
        else {
            $query = "0 as Fiyat".$index.", " .$this->quote($currency) . " as ParaCinsi".$index;
        }

        return $query;
    }

    private function getPriceTypeQueryForStockCardAvailability($parameters, $index)
    {

        $priceType = $parameters['priceType'.$index];
        $currency = $parameters['currency'];
        $endDate = $parameters['endDate'];
        
        if($priceType != '') {
            $p = in_array($priceType, ['ACTFY', 'iACTFY']) ? "a" : "s";
            $factor = $priceType == 'iACTFY' ? "" : "* (100 - dbo.kg_fn_stock_isk(st.Skod, 'S'))/100";
        
            $query = "Cast(Round(Cast(dbo.kg_fn_GetAlSatFiy(st.Skod, isnull(sg.rkod, 0), isnull(bd.bedinx, 0),
                        Substring(".$this->quote($priceType).", 1, 3), ".$this->quote($p).", ".$this->quote($endDate).", ".$this->quote($currency).", '', 'S') ".$factor." as numeric(19, 8)), 2) as float)
                      as Fiyat".$index;
        }
        else {
            $query = "0 as Fiyat".$index;
        }
        
        return $query;
    }
    
    private function getWhereClauseForStockLocationAvailability($parameters)
    {
        $query = [];
        $c = $this->GetConnection();

        if(array_key_exists('sCode', $parameters) && $parameters['sCode']) {
            $query[] = " AND (st.skod=N" .$this->quote($parameters['sCode']) . ") ";
        }
        
        if(array_key_exists('xCode', $parameters) && $parameters['xCode'] && $parameters['xCode'] != '0') {
            $query[] = " AND (isnull(tt.rkod,0)=" .$this->quote($parameters['xCode']) . ") ";
        }
        
        if(array_key_exists('yCode', $parameters) && $parameters['yCode'] && $parameters['yCode'] != '0') {
            $query[] = " AND (isnull(tt.bedkod,0)=" .$this->quote($parameters['yCode']) . ") ";
        }

        return implode(' ', $query);
    }
    
    private function getWhereClauseForStockAvailability($parameters)
    {
        $query = [];
        $c = $this->GetConnection();

        if(array_key_exists('stockCode1', $parameters) && $parameters['stockCode1']) {
            $query[] = " AND (st.skod>=N" .$this->quote($parameters['stockCode1']) . ") ";
        }
        
        if(array_key_exists('stockCode2', $parameters) && $parameters['stockCode2']) {
            $query[] = " AND (st.skod<=N" .$this->quote($parameters['stockCode2']) . ") ";
        }
        
        if(array_key_exists('stockGroup', $parameters) && $parameters['stockGroup']) {
            $query[] = " AND (st.grupkod=" .$this->quote($parameters['stockGroup']) . ") ";
        }
        
        if(array_key_exists('stockType', $parameters) && $parameters['stockType']) {
            $query[] = " AND (st.StokTip=" .$this->quote($parameters['stockType']) . ") ";
        }
        
        if(array_key_exists('barcode', $parameters) && $parameters['barcode']) {
            $query[] = " AND (sb.barcode=" .$this->quote($parameters['barcode']) . ") ";
        }

        if(array_key_exists('option', $parameters) && $parameters['option'] && in_array(strtoupper($parameters['option']), ['MEV', 'MEVSIP', 'MEVSİP']) ) {
            $query[] = " AND ((isnull(tt.Giren,0)-isnull(tt.Cikan,0)-isnull(tt.Gireniade,0)+isnull(tt.Cikaniade,0)) > 0) ";
        }
        
        //parameters from config
        if(array_key_exists('ozkod3', $parameters) && $parameters['ozkod3']) {
            $query[] = " AND (st.ozkod3=" .$this->quote($parameters['ozkod3']) . ") ";
        }

        return implode(' ', $query);
    }
    
    private function getWhereClauseForStockCards($parameters)
    {
        $query = [];

        if(array_key_exists('startDate', $parameters) && !empty($parameters['startDate'])) {
            $query[] = " AND (st.upddt >= " .$this->quote($parameters['startDate']) . ") ";
        }
        
        if(array_key_exists('endDate', $parameters) && !empty($parameters['endDate'])) {
            $query[] = " AND (st.upddt <= " .$this->quote($parameters['endDate']) . ") ";
        }

        if(array_key_exists('stockCode1', $parameters) && $parameters['stockCode1']) {
            $query[] = " AND (st.skod>=N" .$this->quote($parameters['stockCode1']) . ") ";
        }
        
        if(array_key_exists('stockCode2', $parameters) && $parameters['stockCode2']) {
            $query[] = " AND (st.skod<=N" .$this->quote($parameters['stockCode2']) . ") ";
        }
        
        if(array_key_exists('stockGroup', $parameters) && $parameters['stockGroup']) {
            $query[] = " AND (st.grupkod=" .$this->quote($parameters['stockGroup']) . ") ";
        }
        
        if(array_key_exists('stockType', $parameters) && $parameters['stockType']) {
            $query[] = " AND (st.StokTip=" .$this->quote($parameters['stockType']) . ") ";
        }
        
        if(array_key_exists('barcode', $parameters) && $parameters['barcode']) {
            $query[] = " AND (sb.barcode=" .$this->quote($parameters['barcode']) . ") ";
        }

        if(array_key_exists('option', $parameters) && $parameters['option'] && in_array(strtoupper($parameters['option']), ['MEV', 'MEVSIP', 'MEVSİP']) ) {
            $query[] = " AND ((isnull(tt.Giren,0)-isnull(tt.Cikan,0)-isnull(tt.Gireniade,0)+isnull(tt.Cikaniade,0)) > 0) ";
        }
        
        //parameters from config
        if(array_key_exists('ozkod3', $parameters) && $parameters['ozkod3']) {
            $query[] = " AND (st.ozkod3=" .$this->quote($parameters['ozkod3']) . ") ";
        }

        return implode(' ', $query);
    }
    
    private function getCreateQueryForStockAvailability()
    {
        return 
        " if NOT Object_id('tempdb.dbo.#tmpGetStkGMikAsorti') is NULL
            DROP TABLE #tmpGetStkGMikAsorti;

          if NOT Object_id('tempdb.dbo.#tmpGetStkGMik') is NULL
            DROP TABLE #tmpGetStkGMik;

          if NOT Object_id('tempdb.dbo.#tmpGetRezGMikAsorti') is NULL
            DROP TABLE #tmpGetRezGMikAsorti;

          if NOT Object_id('tempdb.dbo.#tmpGetRezGMik') is NULL
            DROP TABLE #tmpGetRezGMik;

          if NOT Object_id('tempdb.dbo.#tmpGetStkeTicMik') is NULL
            DROP TABLE #tmpGetStkeTicMik;

          if NOT Object_id('tempdb.dbo.#tmpGetStkeTicMikAst') is NULL
            DROP TABLE #tmpGetStkeTicMikAst;

          CREATE TABLE [dbo].[#tmpGetStkGMikAsorti]
            (
               [Location]  [varchar](5) NOT NULL,
               [SKod]      [varchar](30) NOT NULL,
               [Rkod]      [int] NOT NULL,
               [Asorti]    [varchar](5) NOT NULL,
               [OzTip]     [varchar](15) NOT NULL,
               [Birim]     [varchar](10) NOT NULL,
               [Giren]     [float] NULL,
               [Cikan]     [float] NULL,
               [Gireniade] [float] NULL,
               [Cikaniade] [float] NULL
            )

          CREATE TABLE [dbo].[#tmpGetStkGMik]
            (
               [Location]  [varchar](5) NOT NULL,
               [SKod]      [varchar](30) NOT NULL,
               [Rkod]      [int] NOT NULL,
               [BedKod]    [int] NOT NULL,
               [OzTip]     [varchar](15) NOT NULL,
               [Birim]     [varchar](10) NOT NULL,
               [Giren]     [float] NULL,
               [Cikan]     [float] NULL,
               [Gireniade] [float] NULL,
               [Cikaniade] [float] NULL
            )

          CREATE TABLE [dbo].[#tmpGetRezGMikAsorti]
            (
               [Location] [varchar](5) NOT NULL,
               [SKod]     [varchar](30) NOT NULL,
               [Rkod]     [int] NOT NULL,
               [Asorti]   [varchar](5) NOT NULL,
               [Birim]    [varchar](10) NOT NULL,
               [Giren]    [float] NULL,
               [Cikan]    [float] NULL,
               [CMKod]    [varchar](15) NOT NULL
            )

          CREATE TABLE [dbo].[#tmpGetRezGMik]
            (
               [Location] [varchar](5) NOT NULL,
               [SKod]     [varchar](30) NOT NULL,
               [Rkod]     [int] NOT NULL,
               [BedKod]   [int] NOT NULL,
               [Birim]    [varchar](10) NOT NULL,
               [Giren]    [float] NULL,
               [Cikan]    [float] NULL,
               [CMKod]    [varchar](15) NOT NULL
            )

          CREATE TABLE [dbo].[#tmpGetStkeTicMik]
            (
               [Location]  [varchar](5) NOT NULL,
               [SKod]      [varchar](30) NOT NULL,
               [Rkod]      [int] NOT NULL,
               [BedKod]    [int] NOT NULL,
               [OzTip]     [varchar](15) NOT NULL,
               [Birim]     [varchar](10) NOT NULL,
               [Giren]     [float] NULL,
               [Cikan]     [float] NULL,
               [Gireniade] [float] NULL,
               [Cikaniade] [float] NULL
            )

          CREATE TABLE [dbo].[#tmpGetStkeTicMikAst]
            (
               [Location]  [varchar](5) NOT NULL,
               [SKod]      [varchar](30) NOT NULL,
               [Rkod]      [int] NOT NULL,
               [Asorti]    [varchar](5) NOT NULL,
               [OzTip]     [varchar](15) NOT NULL,
               [Birim]     [varchar](10) NOT NULL,
               [Giren]     [float] NULL,
               [Cikan]     [float] NULL,
               [Gireniade] [float] NULL,
               [Cikaniade] [float] NULL
            )

          CREATE UNIQUE CLUSTERED INDEX IX_1
            on [#tmpGetStkGMikAsorti] (SKod, RKod, Asorti, Location, Birim, OzTip)

          CREATE NONCLUSTERED INDEX IX_2
            on [#tmpGetStkGMikAsorti] (Location)

          CREATE UNIQUE CLUSTERED INDEX IX_1
            on [#tmpGetStkGMik] (SKod, RKod, BedKod, Location, Birim, OzTip)

          CREATE NONCLUSTERED INDEX IX_2
            on [#tmpGetStkGMik] (Location)

          CREATE UNIQUE CLUSTERED INDEX IX_1
            on [#tmpGetRezGMikAsorti] (SKod, RKod, Asorti, Location, Birim, CMKod)

          CREATE NONCLUSTERED INDEX IX_2
            on [#tmpGetRezGMikAsorti] (Location)

          CREATE UNIQUE CLUSTERED INDEX IX_1
            on [#tmpGetRezGMik] (SKod, RKod, BedKod, Location, Birim, CMKod)

          CREATE NONCLUSTERED INDEX IX_2
            on [#tmpGetRezGMik] (Location)

          CREATE UNIQUE CLUSTERED INDEX IX_1
            on [#tmpGetStkeTicMik] (SKod, RKod, BedKod, Location, Birim, OzTip)

          CREATE NONCLUSTERED INDEX IX_2
            on [#tmpGetStkeTicMik] (Location)

          CREATE UNIQUE CLUSTERED INDEX IX_1
            on [#tmpGetStkeTicMikAst] (SKod, RKod, Asorti, Location, Birim, OzTip)

          CREATE NONCLUSTERED INDEX IX_2
            on [#tmpGetStkeTicMikAst] (Location) 
        ";
    }
    
    private function getOptionSelectQueryForStockAvailability($parameters)
    {
        $query = '';

        $sendLoc = (array_key_exists('sendloc', $parameters) && $parameters['sendloc']) ? $parameters['sendloc'] : '';
        $option = strtoupper($parameters['option']);
        
        if(in_array($option, ['ALLSIP', 'ALLSİP', 'MEVSIP', 'MEVSİP'])) {
            $query = ", (select sum(isnull(si.giren,0)-isnull(si.cikan,0)) from siparis_kay sk left outer join siparis_har sh on sk.sipno=sh.sipno  left outer join si_gchar si on si.fisno=sh.sipno and si.fisHarInx=sh.sipharinx and si.skod=sh.skod  Where (isnull(sk.Durum,'')='') and  (isnull(sh.Durum,'')='') and ((isnull(si.giren,0)-isnull(si.cikan,0))>0)  and sk.location=" .$this->quote($sendLoc) . "  and isnull(sh.skod,'')= isnull(st.skod,'') and isnull(si.rkod,0)=isnull(sg.rkod,0) and isnull(si.bedkod,0)=isnull(bd.bedinx,0)  ) as Siparis ";
        }
        
        return $query;
    }
}