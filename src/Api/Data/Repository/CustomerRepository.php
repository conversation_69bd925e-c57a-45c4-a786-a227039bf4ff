<?php

namespace App\Api\Data\Repository;

use App\Api\Data\Model\Customer;

/**
 * Korgün Sql Server sunucusuna bağlanmak için kullanılacak sınıf
 *
 * <AUTHOR>
 */
class CustomerRepository extends BaseRepository 
{
    /**
     * Gets a customer
     * @param type $parameters
     * @return Customer
     */
    public function getCustomer($parameters)
    {
        $customerCode = array_key_exists('customerCode', $parameters) && $parameters['customerCode'] ? $parameters['customerCode'] : null;
        $identityNumber = array_key_exists('identityNumber', $parameters) && $parameters['identityNumber'] ? $parameters['identityNumber'] : null;
        
        if($customerCode === null && $identityNumber === null) {
            return null;
        }

        $query = "SELECT TOP 1 * FROM musteri_kart WHERE 1=1 ";
        
        if($customerCode !== null) {
            $query .= 'AND mkod=' . $this->quote($customerCode);
        }
        
        if($identityNumber !== null) {
            $query .= 'AND TCKimlikNo=' . $this->quote($identityNumber);
        }

        $result = $this->GetConnection()->prepare($query);
        $result->execute();

        $data = $result->fetch();
        if($data === false) {
            return null;
        }
        
        return (new Customer())->MapFrom($data);
    }
    
    /**
     * Updates/inserts a new customer
     * @param array $parameters
     * @return string customer code
     */
    public function saveCustomer($parameters) 
    {
        $p = $parameters;
        $c = $this->GetConnection();

        if(empty($p['customerName']) || empty($p['location'])) {
            return null;
        }

        if(array_key_exists('customerCode', $p) && !empty($p['customerCode'])) {
            //UPDATE
            $query = "SET NOCOUNT ON 
                      IF EXISTS(SELECT TOP 1 1 FROM Musteri_Kart WHERE MKod = " .$this->quote($p['customerCode']) . ")
                        UPDATE Musteri_Kart 
                        SET " .
                            $this->addEqualParam($p, 'MName', 'customerName', ',') .
                            $this->addEqualParam($p, 'Adr1', 'address1', ',') .
                            $this->addEqualParam($p, 'Adr2', 'address2', ',') .
                            $this->addEqualParam($p, 'Sehir', 'city', ',') .
                            $this->addEqualParam($p, 'PKod', 'postCode', ',') .
                            $this->addEqualParam($p, 'evtel', 'homePhone', ',') .
                            $this->addEqualParam($p, 'istel', 'workPhone', ',') .
                            $this->addEqualParam($p, 'ceptel', 'mobilePhone', ',') .
                            $this->addEqualParam($p, 'Fax', 'fax', ',') .
                            $this->addEqualParam($p, 'eMail', 'email', ',') .
                            $this->addEqualParam($p, 'WWW', 'website', ',') .
                            $this->addEqualParam($p, 'Resim', 'photo', ',') .
                            $this->addEqualParam($p, 'DogTar', 'birthDate', ',', 'ISNULL(NULL,GETDATE())') .
                            $this->addEqualParam($p, 'MedDur', 'maritalStatus', ',') .
                            $this->addEqualParam($p, 'Cinsiyeti', 'gender', ',') . 
                            $this->addEqualParam($p, 'TCKimlikNo', 'identityNumber', ',') . 
                            $this->addEqualParam($p, 'WorkName', 'workName', ',') . 
                            $this->addEqualParam($p, 'WorkAdr1', 'workAddress1', ',') . 
                            $this->addEqualParam($p, 'WorkAdr2', 'workAddress2', ',') . 
                            $this->addEqualParam($p, 'WorkSehir', 'workCity', ',') . 
                            $this->addEqualParam($p, 'WorkFax', 'workFax', ',') . 
                            $this->addEqualParam($p, 'KaraListe', 'blacklist', ',') . 
                            $this->addEqualParam($p, 'ilce', 'county', ',') . 
                            $this->addEqualParam($p, 'Semt', 'district', ',') .
                            $this->addEqualParam($p, 'Ulke', 'country', ',') . 
                            $this->addEqualParam($p, 'OgrenimDurumu', 'educationStatus', ',') . 
                            $this->addEqualParam($p, 'Meslek', 'profession', ',') . 
                            $this->addEqualParam($p, 'vdar', 'taxOffice', ',') . 
                            $this->addEqualParam($p, 'VNo', 'taxNumber');

                    $query = rtrim($query, ','). "                  
                        WHERE MKod = " . $this->quote($p['customerCode']) . "
                        
                        SET NOCOUNT OFF
                        SELECT " .$this->quote($p['customerCode']). " AS mkod";
        }
        else {
            //INSERT
            $p['customerCode'] = $this->getNewCustomerCode($p['location']);
            
            $query = "SET NOCOUNT ON
                      INSERT INTO Musteri_KART (MKOD,MName,Adr1,Adr2,Sehir,PKod,Tel1,Tel2,Tel3,Fax,eMail,WWW,Resim,DogTar,MedDur,Cinsiyeti,Notu,TCKimlikNo,WorkName,WorkAdr1,WorkAdr2,WorkSehir,WorkFax,UyeNo,KaraListe,ilce,Semt,Ulke,OgrenimDurumu,Meslek,VDar,VNo) VALUES (" .
                       $this->quote($p['customerCode']) . ", " .
                       $this->quote($p['customerName']) . ", " .
                       $this->quote($p['address1']) . ", " .
                       $this->quote($p['address2']) . ", " .
                       $this->quote($p['city']) . ", " .
                       $this->quote($p['postCode']) . ", " .
                       $this->quote($p['homePhone']) . ", " .
                       $this->quote($p['workPhone']) . ", " .
                       $this->quote($p['mobilePhone']) . ", " .
                       $this->quote($p['fax']) . ", " .
                       $this->quote($p['email']) . ", " .
                       $this->quote($p['website']) . ", " .
                       $this->quote($p['photo']) . ", " .
                        (array_key_exists('birthDate', $p) && $p['birthDate'] ?$this->quote($p['birthDate']) : "ISNULL(NULL,GETDATE()), ") .
                       $this->quote($p['maritalStatus']) . ", " .
                       $this->quote($p['gender']) . ", " .
                       $this->quote($p['note']) . ", " .
                       $this->quote($p['identityNumber']) . ", " .
                       $this->quote($p['workName']) . ", " .
                       $this->quote($p['workAddress1']) . ", " .
                       $this->quote($p['workAddress2']) . ", " .
                       $this->quote($p['workCity']) . ", " .
                       $this->quote($p['workFax']) . ", " .
                       $this->quote($p['membershipId']) . ", " .
                       $this->quote($p['blacklist']) . ", " .
                       $this->quote($p['county']) . ", " .
                       $this->quote($p['district']) . ", " .
                       $this->quote($p['country']) . ", " .
                       $this->quote($p['educationStatus']) . ", " .
                       $this->quote($p['profession']) . ", " .
                       $this->quote($p['taxOffice']) . ", " .
                       $this->quote($p['taxNumber']) . ")
                    IF NOT EXISTS(Select Top 1 1 from COLOMO where Location=" .$this->quote($p['location']) . " AND MKod=" .$this->quote($p['customerCode']) . ")
                        INSERT INTO COLOMO VALUES ('01'," .$this->quote($p['location']) . ", " .$this->quote($p['customerCode']) . ")

                    SET NOCOUNT OFF
                    SELECT " .$this->quote($p['customerCode']) . " AS mkod";
        }

        $result = $this->GetConnection()->prepare($query);
        $result->execute();
        
        $data = $result->fetch();
        if($data === false) {
            return null;
        }
                
        return $data['mkod'];
    }
    
    /**
     * Generates a new customer code
     * @param type $location
     * @return type
     */
    private function getNewCustomerCode($location)
    {
        $c = $this->GetConnection();
        
        $query = "SELECT ISNULL(Max(MKod)," .$this->quote($location.'0') . ") as MKod from Musteri_Kart Where MKod>=" .$this->quote($location) . " and MKod like " .$this->quote($location.'%');
        
        $result = $this->GetConnection()->prepare($query);
        $result->execute();
        
        $data = $result->fetch();
        if($data === false) {
            return null;
        }

        $id = str_replace($location, '', $data['MKod']);
        $id = (intval($id) + 1) . '';

        $id = str_pad($id, (15 - strlen($location)), '0', STR_PAD_LEFT);
        
        return $location . $id;
    }
}