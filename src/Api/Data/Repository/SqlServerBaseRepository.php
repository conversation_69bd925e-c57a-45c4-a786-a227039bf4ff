<?php
namespace App\Api\Data\Repository;

use App\Api\Data\Interface\ICacheProvider;
use App\Api\Data\Interface\ILoggerDependent;
use Doctrine\Persistence\ManagerRegistry;
use Monolog\Logger;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class SqlServerBaseRepository implements ContainerAwareInterface, ILoggerDependent
{
    /** @var ContainerAwareInterface */
    protected $_container;
    /** @var Logger */
    protected $_logger;
    /** @var ICacheProvider */
    protected $_cacheProvider;

    private ManagerRegistry $doctrine;


    public function __construct(ManagerRegistry $doctrine)
    {
        $this->doctrine = $doctrine;
    }
    
    public function setContainer(ContainerInterface $container = null)
    {
        $this->_container = $container;
    }
        
    public function setLogger(Logger $logger)
    {
        $this->_logger = $logger;
    }

    /**
     * Gets an connection
     *
     * @return object
     */
    protected function GetConnection()
    {
        return $this->doctrine->getConnection('mssql');
    }

}
