<?php
namespace App\Api\Data\Model;

use App\Api\Data\Interface\IMappedEntity;
use Exception;

/**
 * Description of Customer
 *
 * <AUTHOR>
 */
class Order implements IMappedEntity
{
    public $MKod;
    public $SatNo;
    
    public function MapFrom(array $data)
    {
        try
        {
            foreach($data as $k=>$v) {
                $this->{$k} = $v;
            }

            return $this;
        }
        catch(Exception $e)
        {
            throw new Exception('Unable to map Order object: '.$e->getMessage());
        }
    }
}
