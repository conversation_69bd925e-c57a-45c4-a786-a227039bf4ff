<?php
namespace App\Api\Data\Model;

use App\Api\Data\Interface\IMappedEntity;
use Exception;

/**
 * Description of StockCard
 *
 * <AUTHOR>
 */
class StockCard implements IMappedEntity
{
    public $UrunKodu;
    public $UrunTanimi;
    public $Notu;
    public $UrunResmi;
    public $Fiyat1;
    public $ParaCinsi1;
    public $Fiyat2;
    public $ParaCinsi2;
    public $Fiyat3;
    public $ParaCinsi3;
    public $Fiyat4;
    public $ParaCinsi4;
    public $Fiyat5;
    public $ParaCinsi5;
    public $Fiyat6;
    public $ParaCinsi6;
    public $Fiyat8;
    public $ParaCinsi8;
    public $iskonto_y;
    public $iskonto_x;
    public $KdvOran;
    public $BedGrp;
    public $BedGrpTnm;
    public $StokTip;
    public $StokTipTnm;
    public $Birim;
    public $GRUPKOD;
    public $GRUPKODTnm;
    public $CinsKod;
    public $CinsKodTnm;
    public $UreticiKodu;
    public $UreticiKoduTnm;
    public $UMarka;
    public $UMarkaTnm;
    public $Reyon;
    public $ReyonTnm;
    public $OzKod1;
    public $OzKod1Tanim;
    public $OzKod2;
    public $OzKod2Tanim;
    public $OzKod3;
    public $OzKod3Tanim;
    public $OzKod4;
    public $OzKod4Tanim;
    public $OzKod5;
    public $OzKod5Tanim;
    public $OzKod6;
    public $OzKod6Tanim;
    public $OzKod7;
    public $OzKod7Tanim;
    public $OzKod8;
    public $OzKod8Tanim;
    public $OzKod9;
    public $OzKod9Tanim;
    public $OzKod10;
    public $OzKod10Tanim;
    public $OzKod11;
    public $OzKod11Tanim;
    public $OzKod12;
    public $OzKod12Tanim;
    public $OzKod13;
    public $OzKod13Tanim;
    public $OzKod14;
    public $OzKod14Tanim;
    public $OzKod15;
    public $OzKod15Tanim;
    public $OzKod16;
    public $OzKod16Tanim;
    public $OzKod17;
    public $OzKod17Tanim;
    public $OzKod18;
    public $OzKod18Tanim;
    public $OzKod19;
    public $OzKod19Tanim;
    public $OzKod20;
    public $OzKod20Tanim;
    
    public function MapFrom(array $data)
    {
        try
        {
            foreach($data as $k=>$v) {
                $this->{$k} = $v;
            }

            return $this;
        }
        catch(Exception $e)
        {
            throw new Exception('Unable to map StockCard object: '.$e->getMessage());
        }
    }
}
