<?php

namespace App\Api\Controller\Korgun;

use App\Api\Data\Model\Order;
use App\Api\Data\Repository\StockRepository;
use App\Api\Helper\Formatter;
use stdClass;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
#[Route('api/korgun/v1')]
class DefaultController extends BaseController
{
    private StockRepository $stockRepository;

    public function __construct(ParameterBagInterface $parameterBag, StockRepository $stockRepository)
    {
        parent::__construct($parameterBag);
        $this->stockRepository = $stockRepository;
    }

    #[Route('/GetWebStkKart/', methods: ['POST'])]
    public function stockCardsAction(Request $req): JsonResponse
    {
        
        $priceType1 = $req->get('priceType1');
        $priceType2 = $req->get('priceType2');
        $priceType3 = $req->get('priceType3');
        $priceType4 = $req->get('priceType4');
        $priceType5 = $req->get('priceType5');
        $priceType6 = $req->get('priceType6');
        $priceType8 = $req->get('priceType8');
        $currency = $req->get('currency');
        $location = $req->get('location');
        
        $parameters = [
            'startDate' => $req->get('startDate', ''),
            'endDate' => $req->get('endDate', ''),
            'stockCode1' => $req->get('stockCode1', ''),
            'stockCode2' => $req->get('stockCode2', ''),
            'priceType1' => empty($priceType1) ? $this->getApiParameter('fiyattip', '') : $priceType1,
            'priceType2' => empty($priceType2) ? $this->getApiParameter('fiyattip2', '') : $priceType2,
            'priceType3' => empty($priceType3) ? $this->getApiParameter('fiyattip3', '') : $priceType3,
            'priceType4' => empty($priceType4) ? $this->getApiParameter('fiyattip4', '') : $priceType4,
            'priceType5' => empty($priceType5) ? $this->getApiParameter('fiyattip5', '') : $priceType5,
            'priceType6' => empty($priceType6) ? $this->getApiParameter('fiyattip6', '') : $priceType6,
            'priceType8' => empty($priceType8) ? $this->getApiParameter('fiyattip8', '') : $priceType8,
            'currency' => empty($currency) ? $this->getApiParameter('paracinsi', 'TL') : $currency,
            'location' => empty($location) ? $this->getApiParameter('location', '') : $location,
            'stockGroup' => $req->get('stockGroup', ''),
            'stockType' => $req->get('stockType', ''),
            'ozkod3' => $this->getApiParameter('ozelkod3', '')
        ];


        $cards = $this->stockRepository->getStockCards($parameters);


        return $this->json($cards);
    }

    #[Route('/GetWebStkMevDetail/', methods: ['POST'])]
    public function stockAvailabilityAction(Request $req): JsonResponse
    {
        ini_set('max_execution_time', 50000);
        
        $priceType1 = $req->get('priceType1');
        $priceType2 = $req->get('priceType2');
        $priceType3 = $req->get('priceType3');
        $priceType4 = $req->get('priceType4');
        $priceType5 = $req->get('priceType5');
        $priceType6 = $req->get('priceType6');
        $priceType8 = $req->get('priceType8');
        $currency = $req->get('currency');
        $location = $req->get('location');
        
        $parameters = [
            'startDate' => $req->get('startDate', ''),
            'endDate' => $req->get('endDate', ''),
            'stockCode1' => $req->get('stockCode1', ''),
            'stockCode2' => $req->get('stockCode2', ''),
            'priceType1' => empty($priceType1) ? $this->getApiParameter('fiyattip', '') : $priceType1,
            'priceType2' => empty($priceType2) ? $this->getApiParameter('fiyattip2', '') : $priceType2,
            'priceType3' => empty($priceType3) ? $this->getApiParameter('fiyattip3', '') : $priceType3,
            'priceType4' => empty($priceType4) ? $this->getApiParameter('fiyattip4', '') : $priceType4,
            'priceType5' => empty($priceType5) ? $this->getApiParameter('fiyattip5', '') : $priceType5,
            'priceType6' => empty($priceType6) ? $this->getApiParameter('fiyattip6', '') : $priceType6,
            'priceType8' => empty($priceType8) ? $this->getApiParameter('fiyattip8', '') : $priceType8,
            'currency' => empty($currency) ? $this->getApiParameter('paracinsi', 'TL') : $currency,
            'location' => empty($location) ? $this->getApiParameter('location', '') : $location,
            'option' => $req->get('option', ''),
            'barcode' => $req->get('barcode', ''),
            'activeBarcode' => $req->get('activeBarcode', ''),
            'ozkod3' => $this->getApiParameter('ozelkod3', '')
        ];

        $cards = $this->stockRepository->getStockAvailability($parameters);

        return $this->json($cards);
    }

    #[Route('/GetWebStkLocMevDetail/', methods: ['POST'])]
    public function stockLocationAvailabilityAction(Request $req) : JsonResponse
    {
        $location = $req->get('location');

        $parameters = [
            'sCode' => $req->get('sCode', ''),
            'xCode' => $req->get('xCode', '0'),
            'yCode' => $req->get('yCode', '0'),
            'location' => empty($location) ? $this->getApiParameter('location', '') : $location
        ];

        $cards = $this->stockRepository->getStockLocationAvailability($parameters);

        return $this->json($cards);
    }
    
    /**
     * updateTrackingNumberAction
     * @Route("/UpdateTracking/", defaults={"_format"="xml"})
     * @Method({"POST"})
     */
    public function updateTrackingNumberAction()
    {
        $req = $this->getRequest();

        $parameters = [
            'salesNumber' => $req->get('salesNumber', ''),
            'trackingNumber' => $req->get('trackingNumber', '')
        ];
        
        $r = new stdClass();
        $r->Success = $this->_salesRepo->updateTrackingNumber($parameters);

        return new Response($this->_formatter->convertToXml($r));
    }
    
    /**
     * purchaseAction
     * @Route("/SavePurchase/", defaults={"_format"="xml"})
     * @Method({"POST"})
     */
    public function purchaseAction()
    {   
        $req = $this->getRequest();
        //$location = $req->get('location', null);

        $parameters = [
            'customerCode' => $req->get('customerCode', ''),
            'identityNumber' => $req->get('identityNumber', '0'),
            'customerName' => $req->get('customerName', ''),
            'location' => $req->get('location', ''),
            'salesNumber' => $req->get('salesNumber', ''),
            'documentNumber' => $req->get('documentNumber', ''),
            'personnelCode' => $req->get('personnelCode', ''),
            'cashierCode' => $req->get('cashierCode', ''),
            'sellerCode' => $req->get('sellerCode', ''),
            'salesType' => $req->get('salesType', '2'),
            'currency' => $req->get('currency', 'TL'),
            'discountRate' => $req->get('discountRate', '0'),
            'discountAmount' => $req->get('discountAmount', '0'),
            'orderNumber' => $req->get('orderNumber', ''),
            'trackingNumber' => $req->get('trackingNumber', ''),
            'cargoPrice' => $req->get('cargoPrice', ''),            
            'itemIndexes' => $req->get('itemIndex', null),
            'itemCodes' => $req->get('itemCode', null),
            'itemQuantities' => $req->get('itemQuantity', null),
            'itemUnits' => $req->get('itemUnit', null),
            'itemPrices' => $req->get('itemPrice', null),
            'itemCurrencies' => $req->get('itemCurrency', null),
            'itemColourCodes' => $req->get('itemColourCode', null),
            'itemSizeCodes' => $req->get('itemSizeCode', null),
            'itemDiscountAmounts' => $req->get('itemDiscountAmount', null),
            'itemDiscountRates' => $req->get('itemDiscountRate', null),
            'itemVats' => $req->get('itemVat', null),
            'paymentAmounts' => $req->get('paymentAmount', null),
            'paymentTypes' => $req->get('paymentType', null),
            //'paymentIndexes' => $req->get('paymentIndex', null),
            'address1' => $req->get('address1', ''),
            'address2' => $req->get('address2', ''),
            'city' => $req->get('city', ''),
            'postCode' => $req->get('postCode', ''),
            'homePhone' => $req->get('homePhone', ''),
            'workPhone' => $req->get('workPhone', ''),
            'mobilePhone' => $req->get('mobilePhone', ''),
            'fax' => $req->get('fax', ''),
            'email' => $req->get('email', ''),
            'website' => $req->get('website', ''),
            'photo' => $req->get('photo', ''),
            'birthDate' => $req->get('birthDate', ''),
            'maritalStatus' => $req->get('maritalStatus', ''),
            'gender' => $req->get('gender', ''),
            'note' => $req->get('note', ''),
            'workName' => $req->get('workName', ''),
            'workAddress1' => $req->get('workAddress1', ''),
            'workAddress2' => $req->get('workAddress2', ''),
            'workCity' => $req->get('workCity', ''),
            'workFax' => $req->get('workFax', ''),
            'membershipId' => $req->get('membershipId', ''),
            'blacklist' => $req->get('blacklist', ''),
            'county' => $req->get('county', ''),
            'district' => $req->get('district', ''),
            'country' => $req->get('country', ''),
            'educationStatus' => $req->get('educationStatus', ''),
            'profession' => $req->get('profession', ''),
            'taxOffice' => $req->get('taxOffice', ''),
            'taxNumber' => $req->get('taxNumber', ''),
            'platformComment' => $req->get('platformComment', 'web'),
            'platformCommission' => $req->get('platformCommission', null),
            'platformDiscountAmount' => $req->get('platformDiscountAmount',null),
            'cargoBarcode' => $req->get('cargoBarcode',null),
            'cargoCompany' => $req->get('cargoCompany',null),
            'despatchNo' => $req->get('despatchNo'),
        ];
        
        $result = $this->_salesRepo->saveSales($parameters);
        
        if($result === false) {
            $result = new Order();
        }

        return new Response($this->_formatter->convertToXml($result));
    }
}
