<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\ProductAttribute;
use Wb3\SharedBundle\Helper\PaginatorUtil;

/**
 * @extends ServiceEntityRepository<ProductAttribute>
 *
 * @method ProductAttribute|null find($id, $lockMode = null, $lockVersion = null)
 * @method ProductAttribute|null findOneBy(array $criteria, array $orderBy = null)
 * @method ProductAttribute[]    findAll()
 * @method ProductAttribute[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProductAttributeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProductAttribute::class);
    }

    public function allWithPaginate(int $page=1, int $limit=10, array $filters=[]): array
    {
        $qb = $this->createQueryBuilder("pa")
            ->select('pa');
        //if(!empty($filters['q'])) {
        //    $stateOrX = $qb->expr()->orX();
        //    $stateOrX->add('p.skuColor like :qLike');
        //    $stateOrX->add('LOWER(p.title) like LOWER(:qLike)');
        //    $stateOrX->add('pv.barcode = :q');
        //
        //    $qb->setParameter('q', $filters['q']);
        //    $qb->setParameter('qLike', "%".$filters['q']."%");
        //    $qb->andWhere($stateOrX);
        //
        //}
        //attributes
        //if(!empty($filters['productAttributeValueProduct'])) {
        //    $i = 0;
        //    foreach($filters['productAttributeValueProduct'] AS $key => $value) {
        //        $qb->innerJoin('p.attributeValueProducts','pavs'.$i);
        //        $qb->andWhere('pavs'.$i.'.productAttribute = :key'. $key)->setParameter('key'.$key,$key);
        //        $qb->andWhere('pavs'.$i.'.productAttributeValue = :value'.$key)->setParameter('value'.$key,$value);
        //
        //        $i++;
        //    }
        //}

        ////state
        //if(!empty($filters['state'])) {
        //    $qb->andWhere('p.state = :state')->setParameter('state',$filters['state']);
        //}

        $qb->orderBy('pa.updatedAt','DESC');
        $qb->setMaxResults($limit)->setFirstResult(($page-1)*$limit);
        $data = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);
        return $data;
    }
}
