<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\Product;
use Wb3\SharedBundle\Helper\PaginatorUtil;

/**
 * @extends ServiceEntityRepository<Product>
 *
 * @method Product|null find($id, $lockMode = null, $lockVersion = null)
 * @method Product|null findOneBy(array $criteria, array $orderBy = null)
 * @method Product[]    findAll()
 * @method Product[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProductRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Product::class);
    }

    /**
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @return Product[]
     */
    public function allWithPaginate(int $page=1, int $limit=10, array $filters=[]): array
    {
        $qb = $this->createQueryBuilder("p")
        ->select('p');
        $qb->leftJoin('p.variants','pv');
        $qb->leftJoin('pv.locations','pvl');
        if(!empty($filters['q'])) {

            $stateOrX = $qb->expr()->orX();
            $stateOrX->add('p.skuColor like :qLike');
            $stateOrX->add('LOWER(p.title) like LOWER(:qLike)');
            $stateOrX->add('pv.barcode = :q');

            $qb->setParameter('q', $filters['q']);
            $qb->setParameter('qLike', "%".$filters['q']."%");
            $qb->andWhere($stateOrX);

        }
        //attributes
        if(!empty($filters['productAttributeValueProduct'])) {
            $i = 0;
            foreach($filters['productAttributeValueProduct'] AS $key => $value) {
                $qb->innerJoin('p.attributeValueProducts','pavs'.$i);
                $qb->andWhere('pavs'.$i.'.attribute = :key'. $key)->setParameter('key'.$key,$key);
                $qb->andWhere('pavs'.$i.'.value = :value'.$key)->setParameter('value'.$key,$value);

                $i++;
            }
        }

        //state
        if(!empty($filters['state'])) {
            $qb->andWhere('p.state = :state')->setParameter('state',$filters['state']);
        }

            $qb->groupBy('p');
        $qb->orderBy('p.quantity','DESC');
        $qb->setMaxResults($limit)->setFirstResult(($page-1)*$limit);
        $data = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);
        return $data;
    }

    public function allByFilterWithPaginate(int $page=1, int $limit=10, array $filters=[]): array
    {
        $qb = $this->createQueryBuilder("")
            ->select('p')
            ->addSelect('SUM(pv.quantity) as quantity');
        $qb->innerJoin('p.variants','pv');
        $qb->innerJoin('p.attributeValues','pav');
        if(!empty($filters['q'])) {

            $stateOrX = $qb->expr()->orX();
            $stateOrX->add('p.skuColor like :qLike');
            $stateOrX->add('LOWER(p.title) like LOWER(:qLike)');
            $stateOrX->add('pv.barcode = :q');

            $qb->setParameter('q', $filters['q']);
            $qb->setParameter('qLike', "%".$filters['q']."%");
            $qb->andWhere($stateOrX);

        }
        if(!empty($filters['brand_id'])) {
            $qb->andWhere('p.brand = :brand_id')->setParameter('brand_id',$filters['brand_id']);
        }
        if(!empty($filters['season_id'])) {
            //$qb->andWhere('p.p.brand LIKE :brand_id')->setParameter('brand_id',$filter['brand_id']);
        }
            $qb->groupBy('p');
        $qb->setMaxResults($limit)->setFirstResult(($page-1)*$limit);
        $data = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);
        return $data;
    }

    public function getSkusForBuyingPrices() :array
    {
        $qb = $this->createQueryBuilder('p')
            ->select('p.sku')
            //->innerJoin('p.variants','pv','WITH','pv.product = p')
            //->where('pv.quantity > 0')
            ->groupBy('p.sku');

        $data =  $qb->getQuery()->getScalarResult();

        return $data;

    }

    public function checkHash(string $hash, string $sku, ?string $color) : bool
    {
        $qb = $this->createQueryBuilder('p')
            ->select('p.id')
            ->andWhere('p.skuColor = :skuColor')
            ->andWhere('p.hash = :hash')
            ->setParameter('skuColor', $sku." - ".$color)
            ->setParameter('hash', $hash)
            ->setMaxResults(1)
            ->getQuery()->getOneOrNullResult();
        return (bool) $qb;
    }

    public function getBySkuColor(string $sku, ?string $color) : array
    {
        $qb = $this->createQueryBuilder('p')
            ->select('p');
        if(!$color)
            $qb->andWhere('p.sku = :sku')->setParameter('sku',$sku);
        else
            $qb->andWhere('p.skuColor LIKE :color')->setParameter('color', $sku." - ".$color);
        $result =  $qb->getQuery()->setHint(Query::HINT_REFRESH, true)->getResult();
        return $result;
    }
}
