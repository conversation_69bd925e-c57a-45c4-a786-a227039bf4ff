<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\ProductBrand;

/**
 * @extends ServiceEntityRepository<ProductBrand>
 *
 * @method ProductBrand|null find($id, $lockMode = null, $lockVersion = null)
 * @method ProductBrand|null findOneBy(array $criteria, array $orderBy = null)
 * @method ProductBrand[]    findAll()
 * @method ProductBrand[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProductBrandRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProductBrand::class);
    }
}
