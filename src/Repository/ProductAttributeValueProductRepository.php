<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\ProductAttributeValueProduct;

/**
 * @extends ServiceEntityRepository<ProductAttributeValueProduct>
 *
 * @method ProductAttributeValueProduct|null find($id, $lockMode = null, $lockVersion = null)
 * @method ProductAttributeValueProduct|null findOneBy(array $criteria, array $orderBy = null)
 * @method ProductAttributeValueProduct[]    findAll()
 * @method ProductAttributeValueProduct[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProductAttributeValueProductRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProductAttributeValueProduct::class);
    }

    //    /**
    //     * @return ProductAttributeValueProduct[] Returns an array of ProductAttributeValueProduct objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?ProductAttributeValueProduct
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    public function isExist(int $productId, int $attributeId, string $remoteValue) : bool
    {
        $qb = $this->createQueryBuilder('p');
        $qb->join('p.value', 'pav');
        $qb->andWhere('p.product = :product');
        $qb->andWhere('p.attribute = :productAttribute');
        $qb->andWhere('pav.name = :remoteValue');
        $qb->setParameter('product', $productId);
        $qb->setParameter('productAttribute', $attributeId);
        $qb->setParameter('remoteValue', $remoteValue);

        return (bool) $qb->getQuery()->getOneOrNullResult();
    }

    public function listByProduct(int $productId)
    {
        $qb = $this->createQueryBuilder('pavp')
            ->select('pavp,pav,pa,p')
            ->leftJoin('pavp.product', 'p');
        $qb->join('pavp.value', 'pav');
        $qb->join('pavp.attribute', 'pa');
        $qb->andWhere('pavp.product = :productId');
        $qb->setParameter('productId', $productId);
        $qb->orderBy('pa.sort', 'ASC');

        return $qb->getQuery()->getResult();
    }
}
