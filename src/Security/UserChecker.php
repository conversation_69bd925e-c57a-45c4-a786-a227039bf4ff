<?php

namespace App\Security;

use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Core\User\UserCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Wb3\SharedBundle\Entity\User;

class UserChecker implements UserCheckerInterface
{
    public function checkPreAuth(UserInterface $user): void
    {
        if (!$user instanceof User) {
            return;
        }

        if (!$user->isActive()) {
            throw new CustomUserMessageAuthenticationException('Hesabınız devre dışı bırakılmıştır. Lütfen yönetici ile iletişime geçin.');
        }
    }

    public function checkPostAuth(UserInterface $user): void
    {
        // Giriş sonrası kontroller yapılabilir
    }
}