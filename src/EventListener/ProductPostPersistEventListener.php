<?php

namespace App\EventListener;

use App\Service\QueueService;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Events;
use Wb3\SharedBundle\Entity\Product;

#[AsDoctrineListener(event: Events::postPersist)]
class ProductPostPersistEventListener
{
    public function __construct(
        private readonly  QueueService $queueService
    )
    {
    }
    public function postPersist(PostPersistEventArgs $args): void
    {
        $entity = $args->getObject();

        // if this listener only applies to certain entity types,
        // add some code to check the entity type as early as possible
        if (!$entity instanceof Product) {
            return;
        }

        $this->queueService->addQueueForIndexProduct($entity->getId());
    }
}