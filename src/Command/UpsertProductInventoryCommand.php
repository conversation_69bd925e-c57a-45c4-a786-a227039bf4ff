<?php

namespace App\Command;

use App\Service\Korgun\Soap\KorgunSoapMapperService;
use App\Service\Korgun\Soap\KorgunSoapService;
use App\Service\ProductPriceService;
use App\Service\ProductService;
use App\Service\ProductVariantService;
use App\Type\KorgunSoapApi\StkMevDetailResponseType;
use DateInterval;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
    name: 'app:upsert-product-inventory',
    description: 'Ürünlerin stoklarını ve fiyatlarını update eder',
)]
#[AsCronTask('*/10 * * * *')]
class UpsertProductInventoryCommand extends Command
{
    public function __construct(
        private readonly KorgunSoapService       $korgunService,
        private readonly ProductVariantService   $productVariantService,
        private readonly KorgunSoapMapperService $korgunSoapMapperService,
        private readonly LoggerInterface         $upiLogger,
        private readonly ProductPriceService     $productPriceService,
        private readonly ProductService          $productService,
    )
    {
        parent::__construct();
    }


    protected function configure(): void
    {
        $this->addArgument('intervalMin', InputArgument::OPTIONAL, 'Guncelleme suresi. Dakika cinsinden',35);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $intervalMin = $input->getArgument('intervalMin');

        $this->upiLogger->info("Stogu degisen varyantlar aliniyor. intervalMin: " . $intervalMin);
        $startDate = (new DateTime())->sub(DateInterval::createFromDateString($intervalMin . ' minute'));
        $stkMevDetails = $this->korgunService->getStkMevDetail(null,null,null,$startDate);
        $this->upiLogger->info(count($stkMevDetails) . ' adet varyant karti alindi.');
        //211 959.P19K446 - 4121 - 27
        foreach($stkMevDetails as $stkMevDetail) {
            try {
                $stockCode = $this->korgunSoapMapperService->mapStkMevDetailToStockCode($stkMevDetail);
                $this->upiLogger->info("stockCode: " . $stockCode." için işlem yapılıyor.");
                $productVariant = $this->productVariantService->repository->findOneBy(['stockCode'=>$stockCode]);

                if(!$productVariant) {
                    $this->upiLogger->error('Urun bulunadigi icin devam edilemiyorum');
                    continue;
                }
                $this->productService->updateInventory($productVariant);

                //prices sync
                $this->upiLogger->info("prices sync");
                $this->productPriceService->syncFromKorgun($productVariant->getProduct(), $stkMevDetail);
                $this->upiLogger->info("end prices sync");

            } catch (\Exception $e) {
                $this->upiLogger->error("Bir hata ile karşılaşıldı".$e->getMessage());

            }
        }

        return Command::SUCCESS;
    }
}
