<?php

namespace App\Command;

use App\Repository\ProductRepository;
use App\Service\ProductService;
use App\Service\QueueService;
use App\Service\Search\SearchService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ReindexProductCommand extends Command
{
    protected static $defaultName = 'app:reindex-product';
    public function __construct(
        private readonly ProductService $productService,
        private readonly SearchService $searchService,
        private readonly EntityManagerInterface $entityManager,
        private readonly QueueService $queueService,
        string                                    $name = null,
    )
    {
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this->setDescription("Reindex logPlatformApi data for search");
    }

    /**
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        ini_set('memory_limit', '1024M');
        $i = 1;
        $limit=1;
        //remove collection
        //$this->searchService->deleteCollection($_ENV['TYPESENSE_PRODUCT_COLLECTION_NAME']);
        //create schema
        $this->searchService->createDefaultSchemaForProduct();

        do {
            $data = $this->productService->repository->allWithPaginate($i,$limit);

            foreach($data['items'] as $product) {
                $this->queueService->addQueueForIndexProduct($product->getId());
            }
            dump($i*$limit." product indexi icin kuyruga gonderildi...");
            gc_collect_cycles();
            $this->entityManager->clear();
            $i++;
    } while(false);

        dump("bitti");

        return Command::SUCCESS;
    }

}
