<?php

namespace App\Command;
use App\Service\Korgun\Soap\KorgunSoapMapperService;
use App\Service\Korgun\Soap\KorgunSoapService;
use App\Service\ProductPriceService;
use App\Service\ProductService;
use App\Service\QueueService;
use App\Service\Search\SearchService;
use App\Service\TaskService;
use App\Type\KorgunSoapApi\StkKartResponseType;
use DateInterval;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wb3\SharedBundle\Entity\ProductVariant;
use Wb3\SharedBundle\Type\Task\UpdateProduct\UpdateProductRequestTaskType;

#[AsCommand(
    name: 'app:test',
    description: 'Add a short description for your command',
)]
class TestCommand extends Command
{

    public function __construct(
        private readonly ProductService         $productService,
        private readonly TaskService            $taskService,
        private readonly QueueService           $queueService,
        private readonly EntityManagerInterface $em,
        private readonly SearchService $searchService,
        private readonly KorgunSoapService $korgunService,
        private readonly ProductPriceService $productPriceService,
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int

    {
        //$products = $this->productService->repository->findAll();
        //$arraySlice = array_slice($products, 0, 10);
        ///** @var Product $slice */
        //foreach($arraySlice AS $slice) {
        //    $skus[] = $slice->getSku();
        //}
        //dd($this->korgunService->getBuyingPrices());
        //$stkKarts = $this->korgunService->getWebStkKart(60*60*30);
        //$stkMevDetails = $this->korgunService->getWebStkMevDetail($stkKarts[0]->UrunKodu);
        //$product = $this->productService->getProduct(156);
        //
        //$this->productAttributeValueProductService->syncFormKorgun($product, $stkKarts[0], $stkMevDetails[0]);
       // $this->productService->update('OUTLET 85TL-GRC');
        //resimleri search icin getirme
       // $product = $this->productService->repository->findOneBy(['skuColor'=>'109 011G100001 - 13003']);
       // $product->setTitle("CATERPILLAR 011G100001 B21CO044A BOT");
       // $this->em->flush();

//        $this->productService->upsert('211 332.E24Y306',11864);
        //$this->productPriceService->syncBuyingPricesFromKorgun();

        //$upReqType = new UpdateProductRequestTaskType();
        //$upReqType->sku = '186 1648M';
        //
        //$task = $this->taskService->createUpsertProductTask($upReqType);
        //$this->queueService->addQueueToUpdateProduct($task->getId());
        //$this->updateProductScheduler->__invoke();
        //$output->writeln('komut calisti');
        //$this->productService->upsert('05 LA-3720',16646);
        //$p = $this->productService->repository->find(15631);
        //$p->setTitle($p->getTitleOriginal()."-".time());
        //$this->em->flush();
        //bknin veridigi stok olan ürünler

        //upsertProductAll
        ////$stKarts = ['305 S10321K'];
        /// hatalı bulunan %10 sku
        //$stKarts= ['05 COMETKR-G - 8093'];
        //$this->productService->upsertAll('05 COMETKR-G','8093');

        //$skus = ['385 K100953M'];
        //foreach($skus as $sku) {
        //
        //    $this->productService->upsertAllBySku($sku);
        //}
        //$this->productService->upsertAllBySku('305 S10321K');
        //$this->productService->upsert('353 NAVSYDNEY-Z');
       // $this->productService->upsert('305 S10268K',27996);
       // $this->productService->updateByProductId(27996);
        //////
        ////$this->productService->upsert('99 3Y2TS57808');
        //$startDate = (new DateTime())->sub(DateInterval::createFromDateString('35 minute'));
        //$stkKarts = $this->korgunService->getStkKart(null,$startDate);
        ///** @var StkKartResponseType $stkKart */
        //foreach($stkKarts as $stkKart) {
        //    $response = $this->productService->upsert($stkKart->UrunKodu);
        //}
        $skus= ['402 BNT1670'];

        foreach($skus as $sku) {
            $this->productService->upsertAll($sku,null,true);
        }

        return Command::SUCCESS;
    }
}
