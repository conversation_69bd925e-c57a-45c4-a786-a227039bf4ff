<?php

namespace App\Controller;

use App\Form\ProductDesiFormType;
use App\Form\ProductFormType;
use App\Form\AltBarcodeFormType;
use App\Repository\ProductVariantRepository;
use App\Repository\ProductVariantAlternativeBarcodeRepository;
use App\Service\Korgun\Soap\KorgunSoapMapperService;
use App\Service\Korgun\Soap\KorgunSoapService;
use App\Service\ProductAttributeValueProductService;
use App\Service\ProductAttributeValueService;
use App\Service\ProductBrandService;
use App\Service\ProductMapperService;
use App\Service\ProductService;
use App\Service\ProductSmallChangeService;
use App\Service\QueueService;
use App\Service\ProductVariantAlternativeBarcodeService;
use App\Type\Korgun\StkLocMevDetailType;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\Product;
use Wb3\SharedBundle\Entity\ProductVariant;
use Wb3\SharedBundle\Entity\ProductVariantLocation;
use Wb3\SharedBundle\Helper\BreadcrumbItemHelper;
use Wb3\SharedBundle\Helper\FilterUtility;
use App\Service\CampaignItemService;
use Wb3\SharedBundle\Entity\ProductVariantAlternativeBarcode;
use Doctrine\Persistence\ManagerRegistry;

#[Route('/product')]
class ProductController extends BaseController
{
    #[Route('')]
    #[Template('product/index/index.html.twig')]
    public function index(Request $request,
                          ProductService $productService,
                          ProductAttributeValueService $attributeValueService,
                          ProductMapperService $productMapperService,
                          ProductBrandService $brandService
                            ): array
    {
        $isDbQuery = false;
        $filters = FilterUtility::decorateFilter($request->query->all());
        $this->getContent()->addBreadcrumb(new BreadcrumbItemHelper('Ürünler'));
        $page = $request->get('page',1);
        $limit = $request->get('limit',10);
        $brands = $brandService->repository->findBy([],['name'=>'ASC']);
        $states =  [
            Product::STATE_DRAFT => $productMapperService->mapStateToTitle(Product::STATE_DRAFT),
            Product::STATE_WAITING_APPROVE => $productMapperService->mapStateToTitle(Product::STATE_WAITING_APPROVE),
            Product::STATE_APPROVED => $productMapperService->mapStateToTitle(Product::STATE_APPROVED),
            Product::STATE_NOT_APPROVED => $productMapperService->mapStateToTitle(Product::STATE_NOT_APPROVED),
            Product::STATE_PUBLISHED => $productMapperService->mapStateToTitle(Product::STATE_PUBLISHED),
        ];
        $seasons = ['13-14K' => '13-14K','16-17K' => '16-17K','16Y' => '16Y','17-18K' => '17-18K','17Y' => '17Y','18-19K' => '18-19K',
            '18Y' => '18Y','19-20K' => '19-20K','19Y' => '19Y','20-21K' => '20-21K','2010YAZ' => '2010YAZ','2013YAZ' => '2013YAZ','2014YAZ' => '2014YAZ',
            '2015YAZ' => '2015YAZ','20Y' => '20Y','21-22K' => '21-22K','21Y' => '21Y','22-23K' => '22-23K','22Y' => '22Y','23-24K' => '23-24K',
            '23Y' => '23Y','24-25K' => '24-25K','24Y' => '24Y','25Y' => '25Y','MASRAF' => 'MASRAF','PROMOSYON' => 'PROMOSYON','SEZONSUZ' => 'SEZONSUZ',
        ];
        try {
            $pagination = $productService->allWithPaginateFromSearch($filters, $page, $limit);
        } catch (\Exception $e) {
            $pagination = null;
        }
        if(!$pagination) {
            $isDbQuery = true;
            $pagination = $productService->allWithPaginateFromDb($filters, $page, $limit);
        }

        return $this->getContent()->setData([
            'pagination' => $pagination,
            'brands' => $brands,
            'states' => $states,
            'seasons' => $seasons,
            'filters' => $filters,
            'isDbQuery' => $isDbQuery,
            ])->toArray();
    }

    #[Route('/{product}',requirements: ['product' => '\d+'])]
    #[Template('product/edit/edit.html.twig')]
    public function edit(
        Request                             $request,
        Product                             $product,
        ProductAttributeValueProductService $valueProductService,
        ProductMapperService                $productMapperService,
        FormFactoryInterface                $formFactory,
        ProductSmallChangeService           $smallChangeService,
        ProductVariantRepository            $productVariantRepository,
        CampaignItemService                 $promoDiscountPlatformService,
        ProductVariantAlternativeBarcodeRepository $altBarcodeRepository,
        ProductVariantAlternativeBarcodeService $productVariantAlternativeBarcodeService,
        ManagerRegistry $doctrine,

    ): array
    {
        $page = $request->query->getInt('page', 1);
        $limit = $request->query->getInt('limit', 10);

        $smallChange = null;

        $this->getContent()->addBreadcrumb(new BreadcrumbItemHelper('Ürün', 'app_product_index'))
                            ->addBreadcrumb(new BreadcrumbItemHelper($product->getTitle()));

        $filters = FilterUtility::decorateFilter($request->query->all());
        $filters['productId'] = $product->getId();
        $pagination = $promoDiscountPlatformService->allWithPaginate($filters,$page,$limit);
        $alternativeBarcodes = $productVariantAlternativeBarcodeService->allWithPaginate($filters,$page,$limit);



        $form = $formFactory->createNamed('', ProductFormType::class, ['product'=>$product], [
            'data_class' => null,
            'method' => 'post',
            'allow_extra_fields' => true,
            'csrf_protection' => false,
            'attr' => [
                'id' => 'filter-form',
                'class' => 'form d-flex flex-column flex-lg-row'
            ]
        ]);
        $form->handleRequest($request);
        $formDesi = $this->createForm(ProductDesiFormType::class, null, [
            'product' => $product,
        ]);

        $formDesi->handleRequest($request);

        if ($formDesi->isSubmitted() && $formDesi->isValid()) {
            $formData = $formDesi->getData();

            foreach ($product->getVariants() as $variant) {
                $desiKey = 'desi_' . $variant->getId();
                if (isset($formData[$desiKey])) {
                    $newDesi = (float)$formData[$desiKey] ?: null;
                    $productVariantRepository->updateDesi($variant->getId(), $newDesi);
                }
            }
            $this->addFlash('success', 'Desi bilgileri başarıyla kaydedildi.');
        }


        $productAttributeValues = $valueProductService->listByProduct($product->getId());
        $smallChanges = $smallChangeService->listByProduct($product);
        //her zaman tek olacagi icin ilkini al
        if(count($smallChanges))
            $smallChange = $smallChanges[0];




        $alternativeBarcodeForm = $this->createForm(AltBarcodeFormType::class, null, [
            'product' => $product,
        ]);

        $alternativeBarcodeForm->handleRequest($request);

        if ($alternativeBarcodeForm->isSubmitted()) {
            $data = $alternativeBarcodeForm->getData();

            $altBarcode = new ProductVariantAlternativeBarcode();
            $altBarcode->setBarcode($data['barcode']);
            $altBarcode->setProductVariant($data['variant']);

            $em = $doctrine->getManager();
            $em->persist($altBarcode);
            $em->flush();

            $this->addFlash('success', 'Alternatif barkod başarıyla eklendi.');

        }
        return $this->getContent()->setData([
            'product' => $product,
            'prices' => $product->getPrices(),
            'productAttributeValues' => $productAttributeValues,
            'state' => $productMapperService->mapStateToTitle($product->getState()),
            'smallChange' => $smallChange,
            'form' => $form->createView(),
            'formDesi' => $formDesi->createView(),
            'pagination' => $pagination,
            'altBarcodeForm' => $alternativeBarcodeForm->createView(),
            'alternativeBarcodes' => $alternativeBarcodes,
        ])->toArray();
    }

    #[Route('/{product}/_stocks',requirements: ['product' => '\d+'])]
    #[Template('product/edit/_stocks.html.twig')]
    public function stock(Product $product, KorgunSoapService $korgunService,KorgunSoapMapperService $korgunSoapMapperService): array
    {
        $stockData = $korgunData = [];

        /** @var ProductVariant $variant */
        foreach($product->getVariants() as $variant){
            $size = explode(' - ', $variant->getStockCode())[2];
            /** @var ProductVariantLocation $location */
            foreach ($variant->getLocations() as $location){
                $stockData[$size][$location->getLocation()->getCode()] = $location->getQuantity();
            }
        }
        /** @var StkLocMevDetailType[] $locDetails */
        $locDetails = $korgunService->getStkLocMevDetail($product->getSku(), $product->getColor());

        foreach($locDetails as $locDetail){
            $size = $korgunSoapMapperService->mapYTanimToSize($locDetail->YTanim);
            $korgunData[$size][$locDetail->Location] = $locDetail->Miktar;
        }

        return [
            'product' => $product,
            'stockData' => $stockData,
            'korgunData' => $korgunData
        ];
    }

    #[Route('/{product}/update',requirements: ['product' => '\d+'])]
    public function update(Product $product, ProductService $productService): JsonResponse
    {
        $productService->upsertAll($product->getSku(),$product->getColor(),true);
        return new JsonResponse(['success' => true, 'message' => 'Ürün başarıyla güncellendi!'], 200);


    }
    #[Route('/{product}/reindex',requirements: ['product' => '\d+'])]
    public function reindex(string $product, QueueService $queueService)
    {
        $queueService->addQueueForIndexProduct($product);
        return new Response('Kuyruğa gönderildi.');
    }
    
}