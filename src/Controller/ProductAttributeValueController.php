<?php

namespace App\Controller;

use App\Form\ProductAttributeValueFormType;
use App\Service\ProductAttributeService;
use App\Service\ProductAttributeValueService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\ProductAttributeValue;
use Wb3\SharedBundle\Helper\BreadcrumbItemHelper;
use Wb3\SharedBundle\Helper\FilterUtility;

#[Route('/product-attribute-value')]
#[IsGranted('ROLE_CATEGORY_TEAM_LEAD')]
class ProductAttributeValueController extends BaseController
{
    #[Route('')]
    #[Template('product_attribute_value/index/index.html.twig')]
    public function index(Request $request,
                          ProductAttributeValueService $attributeValueService,
                        ProductAttributeService $productAttributeService
    ): array
    {
        $this->getContent()->addBreadcrumb(new BreadcrumbItemHelper('Özellik Değeri'));

        $filters = FilterUtility::decorateFilter($request->query->all());
        $page = $request->get('page',1);
        $limit = $request->get('limit',10);
        //$avBrands = $attributeValueService->listByAttribute(1); // Marka
        $pagination = $attributeValueService->allWithPaginate($filters, $page, $limit);
        $productAttributes = $productAttributeService->repository->findAll();

        return $this->getContent()->setData([
            'pagination' => $pagination,
            'productAttributes' => $productAttributes,
            'filters' => $filters
        ])->toArray();
    }
    #[Route('/{productAttributeValue}',requirements: ['productAttributeValue' => '\d+'])]
    #[Template('product_attribute_value/edit.html.twig')]
    public function edit(Request $request,
                         ProductAttributeValue $productAttributeValue,
                         ProductAttributeValueService $attributeValueService,
                         FormFactoryInterface $formFactory,
                         TranslatorInterface $translator

    ): array | Response
    {
        $this->getContent()->addBreadcrumb(new BreadcrumbItemHelper('Özellik Değeri', 'app_productattribute_index'));
        $this->getContent()->addBreadcrumb(new BreadcrumbItemHelper($productAttributeValue->getName()));


        $form = $formFactory->create(ProductAttributeValueFormType::class, ['productAttributeValue'=>$productAttributeValue], [
            'data_class' => null,
            'method' => 'post',
            'allow_extra_fields' => true,
            'csrf_protection' => false,
            'attr' => [
                'id' => 'filter-form',
                'class' => 'form d-flex flex-column flex-lg-row'
            ]
        ]);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            // $form->getData() holds the submitted values
            // but, the original `$task` variable has also been updated
            $reqData = $form->getData();
            $productAttributeValue->setFriendlyName($reqData['friendlyName']);

            $attributeValueService->update($productAttributeValue);

            //if ($response->data['message'] != '' or $response->data['message'] != null) {
            $this->addFlash('success', $translator->trans('product_attribute_value.success'));
            //} else {
            //    $this->addFlash('error', 'general.error');
            //}

            return $this->redirectToRoute('app_productattributevalue_edit', ['productAttributeValue' => $productAttributeValue->getId()]);
        }


        //$form->handleRequest($request);
        //
        //foreach($product->getPrices() AS $price) {
        //    $productPrices[$price->getProductPriceType()->getId()] = $price->getPrice();
        //}
        //
        //$productAttributeValues = $valueProductService->listByProduct($product->getId());
        //
        return $this->getContent()->setData([
            'productAttribute' => $productAttributeValue,
           'form' => $form->createView()
        ])->toArray();
    }
}