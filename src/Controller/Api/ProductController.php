<?php

namespace App\Controller\Api;
use App\Service\ProductDecoratorService;
use App\Service\ProductService;
use Nelmio\ApiDocBundle\Annotation\Security;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use OpenApi\Attributes as OA;

#[Route('/api')]
class ProductController extends AbstractController
{
    #[OA\Response(
        response: 200,
        description: 'Returns prod',
    )]
    #[OA\Parameter(
        name: 'q',
        description: 'Search parameter',
        in: 'query',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'rewards')]
    #[Security(name: 'Bearer')]
    #[Route('/products', methods: ['GET'])]
    public function index(Request $request,ProductService $productService,ProductDecoratorService $decoratorService): JsonResponse
    {
       // $docarateProducts = [];
       // $filters = $request->query->all();
       //$paginate = $productService->allWithPaginate($filters);
       //foreach($paginate['items'] AS $product) {
       //$docarateProducts[] = $decoratorService->decorateSimple($product);
       //}
       return $this->json(['results' => []]);
    }

}