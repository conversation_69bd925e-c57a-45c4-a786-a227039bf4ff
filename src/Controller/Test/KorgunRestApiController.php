<?php
namespace App\Controller\Test;

use App\Service\Korgun\Rest\KorgunRestService;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Controller\BaseController;

#[Route('/test/korgun-rest-api')]
class KorgunRestApiController extends BaseController
{
    #[Route('/get-stk-kart')]
    public function getStkKart(KorgunRestService $restApiService)
    {
        //?startDate=2024-15-03 16:50:00&endDate=2024-20-03 16:55:00
        $response = $restApiService->getWebStkKart(60*60*24);
        //[
        //    'stockCode1'=>'211 925.B23Y241',
        //    'stockCode2'=>'211 925.B23Y241',
        //]);

        dd($response);
    }
}