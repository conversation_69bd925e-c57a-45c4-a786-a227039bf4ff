<?php

namespace App\Type\Korgun;

class StkKartType
{
	public ?string $UrunKodu;
	public ?string $UrunTanimi;
	public ?string $Notu;
	public ?string $UrunResmi;
	public ?string $Fiyat1;
	public ?string $ParaCinsi1;
	public ?string $Fiyat2;
	public ?string $ParaCinsi2;
	public ?string $Fiyat3;
	public ?string $ParaCinsi3;
	public ?string $Fiyat4;
	public ?string $ParaCinsi4;
	public ?string $Fiyat5;
	public ?string $ParaCinsi5;
	public ?string $Fiyat6;
	public ?string $ParaCinsi6;
	public ?string $Fiyat8;
	public ?string $ParaCinsi8;
	public ?string $iskonto_y;
	public ?string $iskonto_x;
	public ?string $KdvOran;
	public ?string $BedGrp;
	public ?string $BedGrpTnm;
	public ?string $StokTip;
	public ?string $StokTipTnm;
	public ?string $Birim;
	public ?string $GRUPKOD;
	public ?string $GRUPKODTnm;
	public ?string $CinsKod;
	public ?string $CinsKodTnm;
	public ?string $UreticiKodu;
	public ?string $UreticiKoduTnm;
	public ?string $UMarka;
	public ?string $UMarkaTnm;
	public ?string $Reyon;
	public ?string $ReyonTnm;
	public ?string $OzKod1;
	public ?string $OzKod1Tanim;
	public ?string $OzKod2;
	public ?string $OzKod2Tanim;
	public ?string $OzKod3;
	public ?string $OzKod3Tanim;
	public ?string $OzKod4;
	public ?string $OzKod4Tanim;
	public ?string $OzKod5;
	public ?string $OzKod5Tanim;
	public ?string $OzKod6;
	public ?string $OzKod6Tanim;
	public ?string $OzKod7;
	public ?string $OzKod7Tanim;
	public ?string $OzKod8;
	public ?string $OzKod8Tanim;
	public ?string $OzKod9;
	public ?string $OzKod9Tanim;
	public ?string $OzKod10;
	public ?string $OzKod10Tanim;
	public ?string $OzKod11;
	public ?string $OzKod11Tanim;
	public ?string $OzKod12;
	public ?string $OzKod12Tanim;
	public ?string $OzKod13;
	public ?string $OzKod13Tanim;
	public ?string $OzKod14;
	public ?string $OzKod14Tanim;
	public ?string $OzKod15;
	public ?string $OzKod15Tanim;
	public ?string $OzKod16;
	public ?string $OzKod16Tanim;
	public ?string $OzKod17;
	public ?string $OzKod17Tanim;
	public ?string $OzKod18;
	public ?string $OzKod18Tanim;
	 public ?string $OzKod19;
	public ?string $OzKod19Tanim;
	public ?string $OzKod20;
	public ?string $OzKod20Tanim;
}
