<?php

namespace App\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Wb3\SharedBundle\Entity\Product;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wb3\SharedBundle\Entity\ProductAttribute;

class ProductAttributeFormType extends AbstractType
{
public function buildForm(FormBuilderInterface $builder, array $options)
{
    /** @var ProductAttribute $productAttrbute */
    $productAttrbute = $options['data']['productAttribute'];
    $builder->add('name', TextType::class, [
        'label' => 'Özellik Adı',
        'label_attr' => ['class' => 'form-label'],
        'empty_data' => '',
        'attr' => [
            'maxlength' => 90,
            'class'=>'form-control mb-2',
            'value'=>$productAttrbute->getName()
        ],
        'help' => 'Özellik adı gereklidir ve benzersiz olması gerekir.',
        'help_attr' => ['class' => 'text-muted fs-7'],
        'constraints' => [
            new NotBlank(),
            new Length(['max' => 90]),
        ],
    ])->add('description', TextType::class, [
        'mapped' => false,
        'label' => 'Korgün Kodu',
        'label_attr' => ['class' => 'form-label'],
        'empty_data' => 'Korgün tarafında kullanılan kod. Değiştirilemez.',
        'attr' => [
            'class'=>'form-control ckeditor',
            'disabled' => 'disabled',
            'value'=>$productAttrbute->getKorgunCode()
        ],
        'data' => null,
        'help' => null,
        'help_attr' => ['class' => 'text-muted fs-7'],
        'required' => false,
    ])
    ->add('submit', SubmitType::class, [
        'label' => 'Kaydet',
        'attr' => ['class'=>'btn btn-primary mt-5']
    ]);

}

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ProductAttribute::class,
        ]);
    }
}


/*
 * <?php

namespace App\Form;

use Doctrine\ORM\EntityRepository;
use Ide\EntityBundle\Entity\ProductList;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Contracts\Translation\TranslatorInterface;


class ProductListFormType extends AbstractType
{

    private TranslatorInterface $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
/*
public function buildForm(FormBuilderInterface $builder, array $options)
{
    $builder->add('title', TextType::class, [
        'label' => 'product_list.title',
        'empty_data' => '',
        'attr' => ['maxlength' => 90],
        'constraints' => [
            new NotBlank(),
            new Length(['max' => 90]),
        ],
    ])
        ->add('metaTitle')
        ->add('metaKeyword')
        ->add('metaDescription')
        ->add('listType', ChoiceType::class, [
            'required' => true,
            'label' => 'product_list.list_type',
            'empty_data' => null,
            'placeholder' => 'please_select',
            'choices' => [
                $this->translator->trans('product_list.' . ProductList::LIST_TYPE_QUERY_BUILDER) => ProductList::LIST_TYPE_QUERY_BUILDER,
                $this->translator->trans('product_list.' . ProductList::LIST_TYPE_SELECTED) => ProductList::LIST_TYPE_SELECTED
            ]
        ])->add('badge', null, [
            'required' => false,
            'label' => 'Badge',
            'empty_data' => null,
            'placeholder' => 'please_select',
            'query_builder' => function (EntityRepository $er) {
                return $er->createQueryBuilder('b')
                    ->andWhere('b.deletedAt is null');
            },
        ])->add('startedAt', DateType::class, [
            'label' => 'product_list.start_at',
            'required' => true,
            'widget' => 'single_text',
            'html5' => true,
            'attr' => [
                'class' => 'js-datepicker',
                'min' => (new \DateTime('now'))->format('Y-m-d'),
                'max' => (new \DateTime('9999'))->format('Y-m-d')
            ],
            'constraints' => [
                new NotBlank(),
            ],
        ])->add('finishedAt', DateType::class, [
            'label' => 'product_list.finish_at',
            'required' => true,
            'widget' => 'single_text',
            'html5' => true,
            'attr' => [
                'class' => 'js-datepicker',
                'min' => (new \DateTime('now'))->format('Y-m-d'),
                'max' => (new \DateTime('9999'))->format('Y-m-d')
            ],
            'constraints' => [
                new NotBlank()
            ],
        ])
        ->add('isWorkAtNight', CheckboxType::class, [
            'label' => 'general.isWorkAtNight',
            'required' => false
        ]);
    $builder->add('submit', SubmitType::class, ['label' => 'btn.save']);

}

public function configureOptions(OptionsResolver $resolver)
{
    $resolver->setDefaults([
        'data_class' => ProductList::class,
    ]);
}
}
 */