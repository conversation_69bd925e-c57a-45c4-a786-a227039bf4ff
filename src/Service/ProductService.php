<?php

namespace App\Service;


use App\Repository\ProductRepository;
use App\Service\Korgun\Soap\KorgunSoapMapperService;
use App\Service\Korgun\Soap\KorgunSoapService;
use App\Service\LocationService;
use App\Service\ProductCategoryService;
use App\Service\ProductVariantService;
use App\Service\Search\SearchService;
use App\Service\Search\TypeSenseApiService;
use App\Type\KorgunSoapApi\StkKartResponseType;
use App\Type\KorgunSoapApi\StkLocMevDetailResponseType;
use App\Type\KorgunSoapApi\StkMevDetailResponseType;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Wb3\SharedBundle\Entity\Product;
use Wb3\SharedBundle\Entity\ProductCategory;
use Wb3\SharedBundle\Entity\ProductVariant;
use Wb3\SharedBundle\Helper\ObjectHydrator;
use Wb3\SharedBundle\Type\Task\UpdateProduct\UpdateProductRequestTaskType;

/**
 * Bu class aciklamasidir. bilgiler burda olacak.
 */
class ProductService
{
    public static bool $isForceUpdate = false;
    public function __construct(
        private readonly EntityManagerInterface              $em,
        private readonly ProductImageService                 $imageService,
        private readonly ProductPriceService                 $priceService,
        private readonly KorgunSoapService                   $korgunService,
        private readonly KorgunSoapMapperService             $korgunMapperService,
        private readonly ProductVariantService               $variantService,
        private readonly ProductAttributeValueProductService $valueProductService,
        private readonly LoggerInterface                     $logger,
        private readonly SearchService                       $searchService,
        private readonly ProductBrandService                 $brandService,
        public readonly ProductRepository                    $repository,
        private readonly ProductCategoryService              $productCategoryService,
        private readonly ProductVariantLocationService       $productVariantLocationService,
        private readonly ProductMapperService                $productMapperService,
        private readonly TypeSenseApiService                 $typeSenseApiService,
        private readonly ProductVariantService               $productVariantService,
        private readonly KorgunSoapMapperService             $korgunSoapMapperService, private readonly LocationService $locationService,
    )
    {}

    /** Ürünleri belirli filtrelere göre getirir.
     *
     * Ürünleri paginate ve filtrelere göre döndürür.
     *
     */
    public function allWithPaginateFromDb(array $filters = [], int $page = 1, int $limit = 10): array
    {
        return $this->repository->allWithPaginate($page, $limit, $filters);
    }

    public function allWithPaginateFromSearch(array $filters = [], int $page = 1, $limit = 10): ?array
    {
        $products = [];
        $facets = $this->setFilter($filters);

        $response = $this->searchService->search(
            $_ENV['TYPESENSE_PRODUCT_COLLECTION_NAME'],
            $facets,
            $filters['q'] ?? '*',$limit,$page,null,[]);
        if(!$response->success || count($response->data) == 0) {
            return null;
        }
        $data = $response->data;
        //foreach ($data['hits'] as $order) {
        //    $productIds[] = $order['document']['id'];
        //    $searchProducts[$order['document']['id']] = $order['document'];
        //}
        //$products = $this->repository->findBy(['id' => $productIds]);
        foreach ($data['hits'] as $hit) {
            $products[] = $hit['document'];
        }
        //indexi sortla
        usort($products, function ($a, $b) {
            return $a['quantity'] < $b['quantity'];
        });
        return [
            'totalCount' => $data['found'],//Toplam Satır Sayısı
            'itemCount' => count($data['hits']), // Response Dönen Satır Sayısı
            'pageCount' => ceil($data['found'] / $limit), // Toplam Sayfa Sayısı
            'currentPage' => $page, // Şuanki Sayfa
            'limit' => $limit, // items da olması beklenen satır sayısı
            'items' => $products, // Satır Listesi
        ];
    }

    private function setFilter(array $filters): array
    {
        $facets = [];

        if(isset($filters['brandName'])) {
            $facets['brandName'] = $filters['brandName'];
        }
        if(isset($filters['season'])) {
            $facets['season'] = $filters['season'];
        }
        if(isset($filters['state'])) {
            $facets['state'] = $filters['state'];
        }

        return $facets;
    }
    public function getProduct(int $productId): Product
    {

        return $this->repository->find($productId);
    }

    public function getSkusForBuyingPrices(): array
    {
        return $this->repository->getSkusForBuyingPrices();
    }

    /** Ürünün tum SKU larını Korgun database inden günceller
     *
     * @param string $sku
     * @param int|null $color
     * @param StkKartResponseType|null $stkKart
     * @return bool
     */
    public function upsert(string $sku, string $color = null, StkKartResponseType $stkKart=null) : bool
    {
        //$this->em->getConnection()->beginTransaction(); // suspend auto-commit
        //try {
        /**string $sku, int $color = null, StkKartResponseType $stkKart = null */
        $newProducts = $stkMevDetailsGroupBySc = $exProducts = [];

        $this->logger->info("----- {$sku} stok kartı alınıyor. ------");

        if(!$stkKart) {
             $stkResponse = $this->korgunService->getStkKart($sku);
            if(!isset($stkResponse[0])) {
                throw new UnprocessableEntityHttpException('STK_KART_NOT_FOUND');
            }
            $stkKart = $stkResponse[0];
        }
        $stkMevDetails = $this->korgunService->getStkMevDetail($sku);
        $this->logger->info("{$sku} stok kartı ve ilk sc karti alindi.");

        if(count($stkMevDetails) == 0) {
            throw new UnprocessableEntityHttpException('STK_MEV_DETAIL_NOT_FOUND');
        }

        foreach($stkMevDetails AS $stkMevDetail) {
            $stkMevDetailsGroupBySc[$stkMevDetail->getUrunKodu()." - ".$stkMevDetail->getXKod()] = $stkMevDetail;
        }

        foreach ($stkMevDetails AS $stkMevDetail) {

            $sku = trim($stkMevDetail->UrunKodu);
            $skuColor = $sku . " - " . $stkMevDetail->getXKod();
            $YKod = $stkMevDetail->getYKod();
            $size = str_replace(["."],['Adet'],$stkMevDetail->getYTanim());
            $stockCode =  $skuColor . " - " . $size;

            // renk filtresi varsa
            if($color && $stkMevDetail->getXKod() != $color)
                continue;
            //Değişmediyse
            $productHash = hash('sha256', json_encode($stkKart).json_encode($stkMevDetail));
            $sameProductHash = $this->checkHash($productHash,$sku, $color);
            //false && false ise girer
            if(!self::$isForceUpdate && $sameProductHash) {
                $this->logger->info('Ürün değişmediği için güncellenmesi gerekmez.',['sku'=>$sku,'color'=>$stkMevDetail->getXKod()]);
                return false;
            }



            $newProduct = $newProducts[$skuColor]['product'] ?? null;
            $newProductVariant = $newProducts[$skuColor]['variants'][$size] ?? null;

            // newProduct yoksa
            if(!$newProduct) {
                $product = $this->korgunMapperService->mapToProduct($stkKart,$stkMevDetail,$productHash);
                //brand upsert
                $brand = $this->brandService->upsertFromName($stkKart->UMarkaTnm ?? $stkKart->UMarka);
                $product->setBrand($brand);

                $newProducts[$skuColor]['product'] = $newProduct = $product;
            }

            //product sync
            //products a bak ve hash karsilastirmasi yapi
            $exProduct = $this->repository->findOneBy(['skuColor' => $newProduct->getSkuColor()]);
            //if($exProduct)
            //    continue;
            //Eski ürün varsa bilgilerini sync et
            if ($exProduct) {
                //product sync
                if ($exProduct->getHash() != $newProduct->getHash()) { //@TODO burdan ne değişir onlar sorulacak.
                    $exProduct->setTitle($newProduct->getTitle());
                    $exProduct->setTitleOriginal($newProduct->getTitleOriginal());
                    $exProduct->setKorgunState($stkKart->getOzKod3());
                    $exProduct->setState($newProduct->getState());
                    $exProduct->setHash($newProduct->getHash());
                    $exProduct->setUpdatedAt();
                    $this->em->flush();
                }
            } else { // product yoksa ekle
                $this->em->persist($newProduct);
                $this->em->flush();

                $exProduct = $newProduct;
            }

            //image leri wb2 den ekleye
            //images sync
            $this->imageService->syncFromWb2($exProduct);

            $exProducts[$skuColor] = $exProduct;

            // varyant yoksa
            if(!$newProductVariant) {
                if($stkMevDetail->getYTanim() == null || $stkMevDetail->getBarcode() == null)
                    continue;

                    $newProducts[$skuColor]['variants'][$size] = $newProductVariant = $this->korgunMapperService->mapToProductVariant($exProduct, $stkKart, $stkMevDetail);
            }
            //variant sync
            $exVariant = $this->productVariantService->repository->findOneBy(['stockCode'=>$stockCode]);
            if(self::$isForceUpdate || ($exVariant && $exVariant->getHash() != $newProductVariant->getHash()) || !$exVariant) {
                $newProductVariant = $this->variantService->upsert($newProductVariant,$exVariant);

                $exProduct->addVariant($newProductVariant);
                $this->logger->info("$stockCode varyanti değişiklik işlendi.");
            } else {
                $this->logger->info("$stockCode varyantlar değişmedi.");
            }
        } // <end> foreach;


        //renk grubuna gore attributeleri senkronize ediyoruz.) {


        // sync product_category_attribute
        foreach($exProducts AS $sc => $exProd) {
            //prices sync
            $this->logger->info($sc . " prices sync");
            $this->priceService->syncFromKorgun($exProd, $stkMevDetailsGroupBySc[$sc]);
            $this->logger->info($sc . " end prices sync");

            $this->logger->info($sc . " attribute sync");
            $this->valueProductService->syncFormKorgun($exProd, $stkKart, $stkMevDetailsGroupBySc[$sc]);
            $this->logger->info($sc . " end attribute sync");
        }

        $exProduct->setQuantity($exProduct->calculateQuantity());


        //Kategoriyi gelen ozellige gore sync et.
        $this->syncCategoryIdFromKorgun($exProduct, $stkKart->getOzKod22() ?? ProductCategory::ID_DEFAULT);

        $this->logger->info("{$sku} stok kartı kaydediliyor.");
        $this->em->persist($exProduct);
        $this->em->flush();
        $this->em->clear();
        $this->logger->info("{$sku} stok kartı kaydedildi.");
        return true;
    }

    public function updateInventory(ProductVariant $productVariant) : bool
    {
       $sku = $productVariant->getProduct()->getSku();
        $color = $productVariant->getColor();
        $sizeCode = $productVariant->getSizeCode();


        if(is_null($sizeCode)) {
                $this->logger->error('SIZE_CODE_NOT_FOUND', ['variantId' => $productVariant->getId()]);
                return false;
        }
        //variant location sync
        //lokasyon stok bilgilerini al
        $stkMevLocDetails = $this->korgunService->getStkLocMevDetail($sku, $color, $sizeCode);
        //$this->logger->info($skuColor." - ".$size." lokasyon bilgileri güncelleniyor.");
        /** @var StkLocMevDetailResponseType $stkMevLocDetail */
        foreach ($stkMevLocDetails as $stkMevLocDetail) {
            $prodVarLoc = $this->korgunSoapMapperService->mapToProductVariantLocation($productVariant, $stkMevLocDetail);

            $this->productVariantLocationService->upsert($productVariant, $prodVarLoc);
        }

        //@TODO incelenecek. fiyatlar scs den geliyor. her seferinde setliyor.
        $productVariant->setQuantity($productVariant->calculateQuantity());
        $productVariant->getProduct()->setQuantity($productVariant->getProduct()->calculateQuantity());
        $productVariant->getProduct()->setUpdatedAt(); #TODO envanter degfisikliginde
        $productVariant->setUpdatedAt();
        // $exProduct->addVariant($newProductVariant);
        $this->logger->info($productVariant->getStockCode() . " varyanti inventory islendi.");

        $this->em->flush();

        return true;
    }

    public function updateByProductId(int $productId): bool
    {
        $product = $this->repository->find($productId);
        if(!$product) {
            throw  new UnprocessableEntityHttpException('PRODUCT_NOT_FOUND');
        }
        $this->upsert($product->getSku(),$product->getColor());
        return true;
    }

    private function syncCategoryIdFromKorgun(Product $exProduct, string $korgunCategoryId) :void
    {
        if(self::$isForceUpdate || !$exProduct->getCategory() || $exProduct->getCategory()->getId() != (int) $korgunCategoryId) {
            if($category = $this->productCategoryService->repository->find($korgunCategoryId)) {
                $this->em->persist($category);
                $exProduct->setCategory($category);
            }
        }
    }

    public function indexForSearch(int $productId): bool
    {
        $product = $this->repository->find($productId);
        if(!$product) {
            throw  new UnprocessableEntityHttpException('PRODUCT_NOT_FOUND');
        }

        $collectionName = $_ENV['TYPESENSE_PRODUCT_NEW_COLLECTION_NAME'];
        $productType = $this->productMapperService->mapToSearchProductType($product);
        $normalized= ObjectHydrator::normalize($productType);

        $this->typeSenseApiService->call('POST','/collections/'.$collectionName.'/documents',$normalized,['action'=>'upsert']);

        return true;
    }

    private function checkHash(string $hash,string $sku, ?string $color) : bool
    {
        return $this->repository->checkHash($hash, $sku, $color);
    }

    public function upsertAll(string $sku, string $color = null,bool $isForceUpdate = false): bool
    {
        self::$isForceUpdate = $isForceUpdate;
        $stkKartRes = $this->korgunService->getStkKart($sku);
        if(!isset($stkKartRes[0])) {
            throw new UnprocessableEntityHttpException('STK_KART_NOT_FOUND');
        }
        $stkKart = $stkKartRes[0];

        //Özellikleri update et
        $this->upsert($sku,$color,$stkKart);

        $prods =  $this->repository->getBySkuColor($sku, $color);

        //Stokları update et
        foreach($prods AS $prod) {
            foreach ($prod->getVariants() AS $variant) {
                $this->updateInventory($variant);
            }
            $this->indexForSearch($prod->getId());
        }
        return true;
    }
}