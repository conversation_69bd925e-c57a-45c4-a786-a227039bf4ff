<?php

namespace App\Service;

use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Wb3\SharedBundle\Entity\Product;
use Wb3\SharedBundle\Entity\ProductAttribute;
use Wb3\SharedBundle\Entity\ProductAttributeValue;
use App\Repository\ProductAttributeValueRepository;
use Wb3\SharedBundle\Entity\ProductAttributeValueProduct;

class ProductAttributeValueService
{

    public ProductAttributeValueRepository $repository;
    private EntityManagerInterface $em;
    private LoggerInterface $logger;

    public function __construct(
        ProductAttributeValueRepository $repository,
        EntityManagerInterface $em,
        LoggerInterface $logger
    )
    {
        $this->repository = $repository;
        $this->em = $em;
        $this->logger = $logger;
    }

    public function findOrCreate(ProductAttribute $attribute, string $remoteValue)
    {
        $value = $this->repository->findOneBy(['attribute'=>$attribute,'name'=>$remoteValue]);
        if(!$value) {
            $value = new ProductAttributeValue();
            $value->setAttribute($attribute);
            $value->setName($remoteValue);
            $value->setCreatedAt();
            $value->setUpdatedAt();
            $this->em->persist($value);
            //$this->em->flush();
            $this->logger->info('{attributeName} için {valueName} değeri oluşturuldu.',
                ['attributeName'=>$attribute->getName(),'valueName'=>$value->getName()]);
        }
        return $value;
    }

    public function listByAttribute(int $attributeId) : array
    {
        return $this->repository->findBy(['attribute'=>$attributeId],['name'=>'ASC']);
    }

    public function allWithPaginate(array $filters = [], int $page = 1, $limit = 10): array
    {

        return $this->repository->allWithPaginate($page, $limit, $filters);
    }

    public function update(ProductAttributeValue $productAttributeValue) : bool
    {
        $this->em->flush();

        return true;
    }

    public function getOneFromAttributesByProduct(Product $product, int $attributeId) : string
    {
        $filterAttributes = $product->getAttributes()->filter(function($attribute) use ($attributeId) {return $attribute->getAttribute()?->getId() == $attributeId;});
        if($filterAttributes->isEmpty()) {
            return '';
        }
        /** @var ProductAttributeValueProduct $filteredAttribute */
        $filteredAttribute = $filterAttributes->first();


        return $filteredAttribute->getValue()->getFriendlyName() ?? $filteredAttribute->getValue()->getName();
    }
}