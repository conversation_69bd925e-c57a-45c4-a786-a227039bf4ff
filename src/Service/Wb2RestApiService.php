<?php
namespace App\Service;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Security;
use Psr\Log\LoggerInterface;

class Wb2RestApiService extends RestApiService
{
    public function __construct(/*Security $security,*/ LoggerInterface $logger, TokenStorageInterface $tokenStorage, ParameterBagInterface $parameter)
    {
        parent::__construct( $logger, $tokenStorage, $parameter);
        $this->apiEndpoint = $parameter->get('WB2_API_URL');
    }

}