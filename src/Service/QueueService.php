<?php

namespace App\Service;

use Enqueue\Client\Message;
use Enqueue\Client\ProducerInterface;
use Enqueue\Rpc\Promise;

class QueueService
{
public function __construct(
    private readonly ProducerInterface $producer
)
{
}


    public function addQueueForUpsertProductAll(int $taskId): ?Promise
    {
        $serialized = $taskId;
        $message = new Message($serialized);
        return  $this->producer->sendCommand('UpsertProductAllProcessor', $message);
    }

    public function addQueueForIndexProduct(int $productId): ?Promise
    {
        $serialized = $productId;
        $message = new Message($serialized);
        return  $this->producer->sendCommand('IndexProductProcessor', $message);
    }
}