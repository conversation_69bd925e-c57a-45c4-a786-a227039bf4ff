<?php
namespace App\Service;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use App\Repository\ProductVariantAlternativeBarcodeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Wb3\SharedBundle\Entity\ProductVariantAlternativeBarcode;

class ProductVariantAlternativeBarcodeService
{
    public ProductVariantAlternativeBarcodeRepository $repository;
    public EntityManagerInterface $em;

    public function __construct(ManagerRegistry $registry, ProductVariantAlternativeBarcodeRepository $repository,EntityManagerInterface $em)
    {
        $this->em = $em;


        $this->repository = $repository;
    }


    public function allWithPaginate(array $filters = [], int $page = 1, $limit = 10): array
    {

        return $this->repository->allWithPaginate($page, $limit, $filters);
    }
    public function insert(ProductVariantAlternativeBarcode $productVariantAlternativeBarcode)
    {
        $this->em->persist($productVariantAlternativeBarcode);
        $this->em->flush();
    }

}