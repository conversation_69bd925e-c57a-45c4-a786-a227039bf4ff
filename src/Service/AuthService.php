<?php

namespace App\Service;

use App\Type\Auth\LoginRequestType;
use App\Type\Auth\LoginResponseType;
use App\Type\Auth\PasswordForgotType;
use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\NonUniqueResultException;
use Wb3\SharedBundle\Entity\User;
use Wb3\SharedBundle\Entity\UserToken;
use App\Repository\UserRepository;
use App\Repository\UserTokenRepository;
use Doctrine\ORM\EntityManagerInterface;
use Wb3\SharedBundle\Helper\StringUtility;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\PreconditionRequiredHttpException;
use Symfony\Component\PasswordHasher\Hasher\PasswordHasherFactoryInterface;
use Firebase\JWT\JWT;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

class AuthService
{
    /**
     * @var EntityManagerInterface
     */
    private $em;
    /**
     * @var ParameterBagInterface
     */
    private $params;
    /**
     * @var UserRepository
     */
    private $userRepository;
    /**
     * @var UserTokenRepository
     */
    private $userTokenRepository;
    /**
     * @var PasswordHasherFactoryInterface
     */
    private $passwordHasherFactory;
    /**
     * @var LogService
     */
    private $logService;
    /**
     * @var LdapService
     */
    private $ldapService;
    /**
     * @var RouterInterface
     */
    private $router;
    /**
     * @var NotificationService
     */
    private $notificationService;

    public function __construct(
        EntityManagerInterface         $em,
        ParameterBagInterface          $params,
        UserRepository                 $userRepository,
        PasswordHasherFactoryInterface $passwordHasherFactory,
        RouterInterface                $router,
    )
    {
        $this->em = $em;
        $this->params = $params;
        $this->userRepository = $userRepository;
        $this->passwordHasherFactory = $passwordHasherFactory;
        $this->router = $router;
    }

    public function findUserWithUserName($userName): ?User
    {
        return $this->userRepository->findOneBy(['email' => $userName]);
    }

    public function findUserWithId($id): ?User
    {
        return $this->userRepository->find($id);
    }

    /**
     * @param User   $user
     * @param string $password
     *
     * @return bool
     */
    public function checkCredentials(User $user, string $password): bool
    {
        if ($this->passwordHasherFactory->getPasswordHasher(User::class)->verify($user->getPassword(), $password, null)) {
            return true;
        }
        return false;
    }


    /**
     * @param LoginRequestType $loginRequest
     *
     * @return UserToken|null
     * @throws NonUniqueResultException
     */
    public function loginWithPassword(LoginRequestType $loginRequest): ?UserToken
    {
        $user = $this->findUserWithUserName($loginRequest->userName);
        if (!$user) {
            throw new AccessDeniedHttpException('INVALID_PASSWORD');
        }

            if (!$this->checkCredentials($user, $loginRequest->password)) {
                $this->renderUserLock($user);
                throw new AccessDeniedHttpException('INVALID_PASSWORD');
            }

        if (!$this->checkPlatformCanLogin($loginRequest->platform, $user)) {
            throw new AccessDeniedHttpException('ACCOUNT_HAS_NO_PERMISSION_TO_PLATFORM');
        }

        if ($user->getForgotPassRequestCounter() > 0 || $user->getForgotPassRequestExpireAt()) {
            $user->setForgotPassRequestCounter(0);
            $user->setForgotPassRequestExpireAt(null);
            $this->em->flush();
        }

        return $this->renderLogin($user, $loginRequest->platform, $loginRequest->clientIp);
    }

    /**
     * @param User $user
     * @param      $platform
     * @param      $clientIp
     *
     * @return UserToken|null
     * @throws NonUniqueResultException
     */
    private function renderLogin(User $user, $platform, $clientIp): ?UserToken
    {
        //check user eligible
        $this->validateUserForLogin($user);

        $token = $this->createOrUpdateToken($user, $platform, $clientIp);
        $this->createLoginLog($user, $platform, $clientIp);

        /** Clean user lock*/
        $this->cleanUserLock($user);

        return $token;
    }

    /**
     * @param PasswordForgotType $forgotType
     *
     * @return User|void
     */
    public function forgotPass(PasswordForgotType $forgotType)
    {
        $user = $this->userRepository->findOneBy(['email' => $forgotType->userName]);
        if (!$user) {
            throw new PreconditionRequiredHttpException('CHECK_USER_INFO');
        }

        $this->userForgotPass($user);

        return $user;
    }

    /**
     * @param User $user
     *
     * @return void
     */
    public function userForgotPass(User $user)
    {

        if ($user->getForgotPassRequestExpireAt() && $user->getForgotPassRequestExpireAt()->getTimestamp() > time()) {
            throw new PreconditionRequiredHttpException('USER_ALREADY_HAS_FORGOT_PASS_INFO');
        } else {
            $count = $user->getForgotPassRequestCounter() + 1;
            $user->setForgotPassRequestCounter($count);
            if ($count == 3) {
                $dater = new DateTime();
                $user->setForgotPassRequestExpireAt($dater->modify('+1 days'));
                $user->setForgotPassRequestCounter(0);
            }
        }

        $dater = new DateTime();
        $user->setUpdatedAt(new DateTime());
        $user->setForgotPassHash(StringUtility::generateHash());
        $user->setForgotPassHashExpireAt($dater->modify('+1 days'));
        $this->em->flush();

        if($user->getVendor())
        {
            $this->notificationService->forgotVendorPass($user, $user->getForgotPassHash());
        }else{
            $loginLink = $this->generateUserLoginLink($user);
            $this->notificationService->forgotPass($user, $loginLink);
        }

    }

    /**
     * @param $userId
     *
     * @return User|null
     */
    public function apiKeyAuthenticationLoginCheck($userId): ?User
    {
        $user = $this->findUserWithId($userId);
        $this->validateUserForLogin($user);

        return $user;
    }

    /**
     * @param $token
     * @param $platform
     *
     * @return UserToken
     */
    public function disableTokenData($token, $platform): UserToken
    {
        $userToken = $this->userTokenRepository->findByToken($token, $platform);
        $userToken->setExpireAt(new DateTime());
        $this->em->flush();

        return $userToken;
    }

    /**
     * @param User $user
     *
     * @return User
     */
    public function disableAllTokens(User $user): User
    {
        $tokens = $this->userTokenRepository->getActiveTokens($user);
        foreach ($tokens as $token) {
            $token->setExpireAt(new DateTime());
            $this->em->flush();
        }

        return $user;
    }

    /**
     * @param User $user
     * @param      $platform
     * @param      $clientIp
     *
     * @return UserToken|null
     * @throws NonUniqueResultException
     */
    public function refreshToken(User $user, $platform, $clientIp): ?UserToken
    {
        return $this->createOrUpdateToken($user, $platform, $clientIp);
    }

    /**
     * @param User $user
     * @param      $platform
     * @param      $clientIp
     *
     * @return UserToken|null
     * @throws NonUniqueResultException
     */
    private function createOrUpdateToken(User $user, $platform, $clientIp): ?UserToken
    {
        $expireTime = strtotime('+7 day');
        $userData = [
            'email' => $user->getEmail(),
            'userId' => $user->getId(),
            'firstName' => $user->getFirstname(),
            'lastName' => $user->getLastname(),
            'expireTime' => $expireTime,
            'platform' => $platform,
            'role' => $user->getRoles(),
          //  'vendor' => ($user->getVendor()) ? $user->getVendor()->getId() : null
        ];

        $secret = $this->params->get('auth_secret');
        $token = JWT::encode($userData, $secret);

        $refreshToken = hash_hmac(
            'SHA256',
            $platform . $user->getId() . microtime(true) . rand(1, 10000),
            $secret
        );

        $userToken = $this->userTokenRepository->findTokenByUser($user, $platform);
        if (!$userToken) {
            $userToken = new UserToken();
            $userToken->setPlatform($platform);
            $userToken->setUser($user);
            $userToken->setCreatedAt();
        }

        $userToken->setRefreshToken($refreshToken);
        $userToken->setAccessToken($token);
        $userToken->setPlatform($platform);
        $userToken->setExpireAt(new DateTimeImmutable(date('Y-m-d H:i:s', $expireTime)));
        $userToken->setUpdatedAt();
        $userToken->setClientIp($clientIp);
        $this->em->persist($userToken);
        $this->em->flush();

        return $userToken;
    }

    /**
     * @param $platform
     * @param $user
     *
     * @return bool
     */
    private function checkPlatformCanLogin($platform, $user): bool
    {
        $groups = $user->getPermissionGroups();
        foreach ($groups as $group) {
            if ($group->getRoles()) {
                foreach ($group->getRoles() as $role) {
                    if ($role->getPlatform() == $platform) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * @param User $user
     *
     * @return User
     */
    public function cleanUserLock(User $user): User
    {
        if ($user->getLoginErrorCount() > 0 || $user->getLockedAt()) {
            $user->setLockedAt(null);
            $user->setLoginErrorCount(null);
            $user->setUpdatedAt(new DateTime());
            $this->em->flush();
        }

        return $user;
    }

    /**
     * @param User $user
     *
     * @return void
     */
    private function renderUserLock(User $user)
    {
        if ($user->getLoginErrorCount() != null && $user->getLoginErrorCount() == 5) {
            $user->setLockedAt(new DateTimeImmutable());
            $user->setUpdatedAt();

            $this->em->flush();
            throw new AccessDeniedHttpException('ACCOUNT_LOCKED_TOMANY_RETRY');
        } else {
            $loginErrorCount = ($user->getLoginErrorCount()) ? $user->getLoginErrorCount() : 0;
            $user->setLoginErrorCount($loginErrorCount + 1);
            $user->setUpdatedAt();

            $this->em->flush();
        }
    }

    /**
     * @param User $user
     *
     * @return bool
     */
    private function requiredChangePassword(User $user): bool
    {
        if (!empty($user->getChangePasswordAt())) {
            $dateDiff = date_diff(new DateTime(), $user->getChangePasswordAt());
            if ($dateDiff->days < 30) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param User $user
     *
     * @return void
     */
    private function validateUserForLogin(User $user)
    {
        if ($user->getLockedAt()) {
            throw new AccessDeniedHttpException('ACCOUNT_LOCKED_TOMANY_RETRY');
        }
        if (!$user->isEnabled()) {
            throw new AccessDeniedHttpException('ACCOUNT_IS_NOT_ACTIVE');
        }
    }
}
