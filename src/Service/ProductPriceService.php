<?php

namespace App\Service;


use App\Repository\SqlServerRepository;
use App\Service\Korgun\Soap\KorgunSoapService;
use App\Type\Korgun\BuyingPrice;
use App\Type\Korgun\StkMevDetail;
use App\Type\Korgun\BuyingPriceType;
use Doctrine\Common\Collections\Collection;
use App\Type\KorgunSoapApi\BuyingPriceResponseType;
use App\Type\KorgunSoapApi\StkMevDetailResponseType;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Wb3\SharedBundle\Entity\Product;
use Wb3\SharedBundle\Entity\ProductAttributeValueProduct;
use Wb3\SharedBundle\Entity\ProductPrice;
use Wb3\SharedBundle\Helper\ObjectHydrator;
use App\Repository\ProductPriceRepository;
use App\Repository\ProductRepository;

class ProductPriceService
{

    public function __construct(
        private readonly  ProductPriceRepository $repository,
        private readonly EntityManagerInterface $em,
        private readonly LoggerInterface $logger,
        private readonly SqlServerRepository $sqlServerRepository,
        private readonly ProductRepository $productRepository,
        private readonly ProductPriceTypeService $productPriceTypeService,
        private readonly KorgunSoapService $korgunService,
    ){}

    public function update(ProductPrice $productPrice)
    {
        //$this->em->flush();
    }

    public function insert(Product $product,ProductPrice $productPrice)
    {
        //$this->em->persist($productPrice);
        $product->addPrice($productPrice);
        //$this->em->flush();
    }

    public function syncFromKorgun(Product $product, ?StkMevDetailResponseType $stkMevDetail=null): void
    {
        /**
         * Satis fiyatlari
         * 1- TAKSİTLİ (PER.SAT.)
         * 2- PEŞİN (İND. PER. SAT.)
         * 3- İNTERNET FİYAT
         * 4- PEŞIN ORJİNAL FİYATI
         * 5- AYON FİYAT
         * 9- USD
         *
         *
         * <Fiyat1>1399.9000000000001</Fiyat1>
         * <ParaCinsi1>TL</ParaCinsi1>
         * <Fiyat2>1399.9000000000001</Fiyat2>
         * <ParaCinsi2>TL</ParaCinsi2>
         * <Fiyat3>1399.9000000000001</Fiyat3>
         * <ParaCinsi3>TL</ParaCinsi3>
         * <Fiyat4>0.0</Fiyat4>
         * <ParaCinsi4>TL</ParaCinsi4>
         * <Fiyat5>0</Fiyat5>
         * <ParaCinsi5>TL</ParaCinsi5>
         * <Fiyat6>0</Fiyat6>
         * <ParaCinsi6>TL</ParaCinsi6>
         * <Fiyat8>0.0</Fiyat8>
         * <ParaCinsi8>TL</ParaCinsi8>
         */

        if($stkMevDetail == null) {
            $stkMevDetails = $this->korgunService->getStkMevDetail($product->getSku());
            foreach($stkMevDetails AS $remoteStkMevDetail) {
                if($remoteStkMevDetail->getXKod() == $product->getColor()) {
                    $stkMevDetail = $remoteStkMevDetail;
                    break;
                }
            }
        }

        if($stkMevDetail == null) {
            throw new UnprocessableEntityHttpException('STK_MEV_DETAIL_NOT_FOUND');
        }

        $productPriceTypes = $this->productPriceTypeService->repository->findBy(['type'=>'sales']);

        //olan fiyatları kaydet
        foreach($productPriceTypes AS $ppt) {
            $funcName = "get".ucfirst($ppt->getKorgunCode());
            if(method_exists($stkMevDetail,$funcName) && $price = $stkMevDetail->$funcName()) {
                $this->upsert($ppt->getId(), $price, $product);
            }
        }
        $this->em->flush();
        $this->em->clear();
    }

    private function upsert(int $productPriceTypeId, float $price, Product $product) : void
    {

        $hash = hash('sha256', $price);
        $ppt = $this->productPriceTypeService->repository->find($productPriceTypeId);
        $ppNew = new ProductPrice();
        $ppNew->setType($ppt)
            ->setPrice($price)
            ->setProduct($product)
            ->setCreatedAt()
            ->setUpdatedAt()
            ->setHash($hash);

        $ppExt = $this->repository->findOneBy(['product'=>$product,'type'=>$ppt]);
        if($ppExt) {
            if($ppExt->getHash() != $ppNew->getHash()) {
                //Fiyat değişimi var
                $ppExt->setPrice($price)
                    ->setUpdatedAt()
                    ->setHash($hash);
                $this->update($ppExt);
            }
        } else {
            $this->insert($product, $ppNew);
        }
    }

    /**
     * Alış fiyatlarını Korgun'dan çeker
     */
    private function getBuyingPricesFromKorgun(): array
    {
        $skus = $this->productRepository->getSkusForBuyingPrices();
        $skus = array_column($skus,'sku');

        //@TODO Alış fiyatları servisten alınancak;
        throw new Exception('not implement yet!');

        $response = $this->sqlServerRepository->getBuyingPrices($skus);
        $data = array_map(function($product){return ObjectHydrator::hydrate($product,new BuyingPriceResponseType());},$response);
        return $data;
    }

    /**
     * Alış fiyatlarını Korgun'dan sync eder
     */
    public function syncBuyingPricesFromKorgun() : void  {
        //Alış fiyatlarını sync et
        /** @var BuyingPriceType[] $buyingPrices */
        $this->logger->info('Alış fiyatları alınıyor.');
        $buyingPrices = $this->getBuyingPricesFromKorgun();
        $this->logger->info(count($buyingPrices).' adet alış fiyatı alındı.');
        foreach($buyingPrices AS $buyingPrice) {
            //alis fiyatinin tip idsi 1
            $products = $this->productRepository->findBy(['sku'=>$buyingPrice->sku]);
            foreach($products AS $product) {
                $this->upsert(9, $buyingPrice->buyingPrice, $product); //alış fiyatı
                $this->upsert(10, ($buyingPrice->buyingPrice*1.3) + 40, $product); //uyarı fiyatı
                $this->upsert(11, $buyingPrice->buyingPrice, $product); //kritik fiyat

                $this->logger->info($product->getSkuColor().' - alış fiyatı güncellendi.');
            }
        }
        $this->logger->info('Alış fiyatları güncellendi.');
    }

    public function getOneFromProductByType(Product $product, int $typeId) : float
    {
        $filterPrices = $product->getPrices()->filter(function($price) use ($typeId) {return $price->getType()->getId() == $typeId;});

        if($filterPrices->isEmpty()) {
            return 0;
        }

        /** @var ProductPrice $filterPrice */
        $filterPrice = $filterPrices->first();

        return $filterPrice->getPrice();
    }
}