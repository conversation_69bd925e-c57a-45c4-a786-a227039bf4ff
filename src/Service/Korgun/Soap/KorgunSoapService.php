<?php

namespace App\Service\Korgun\Soap;
use App\Type\KorgunSoapApi\StkKartResponseType;
use App\Type\KorgunSoapApi\StkLocMevDetailResponseType;
use App\Type\KorgunSoapApi\StkMevDetailResponseType;
use DateInterval;
use DateTime;
use Wb3\SharedBundle\Helper\ObjectHydrator;

class KorgunSoapService
{

    private KorgunSoapClient  $apiService;

    public function __construct(KorgunSoapClient $apiService)
    {
        $this->apiService = $apiService;
    }

    /** Verilen time veua sku ya gore değişen son stok kartlarinin bilgilerini getirir. */
    public function getStkKart(string $sku = null, DateTime $startDate=null) : array
    {
        $parameters = [
            'tar1' =>  ($startDate) ? $startDate->format('Y-d-m H:i:s') : '',
            'tar2' => ($startDate) ? (new DateTime())->format('Y-d-m H:i:s') : '',
            'skod1' => $sku ?? '',
            'skod2' => $sku ?? '',
            'FiyTip1' => '',
            'FiyTip2' => '',
            'FiyTip3' => '',
            'FiyTip4' => '',
            'FiyTip5' => '',
            'FiyTip6' => '',
            'FiyTip7' => '',
            'FiyTip8' => '',
            'FiyTip9' => '',
            'FiyTip10' => '',
            'Location' => '',
            'ParaCinsi' => '',
            'StkGrp' => '',
            'StkTip' => '',
        ];

        $response = $this->apiService->call("GetWebStkKart",$parameters);
        $data = array_map(function($product){return ObjectHydrator::hydrate((array) $product,new StkKartResponseType());},$response->data ?? []);

        return $data;
    }

    /** Verilen sku ve zaman aralığına göre stok kartlarının renk beden kırılımlarının stok hareketlerini getirir.
     *
     * @return Array<StkMevDetailResponseType>*/
    public function getStkMevDetail(string $sku = null, string $barcode=null, array $locations=null, DateTime $startDate=null): array
    {

        $parameters = [
            'tar1' => ($startDate) ? $startDate->format('Y-d-m H:i:s') : '',
            'tar2' => ($startDate) ? (new DateTime())->format('Y-d-m H:i:s') : '',
            'skod1' => $sku ?? '',
            'skod2' => $sku ?? '',
            'bedgrup' =>'',
            'FiyTip1' => '',
            'FiyTip2' => '',
            'FiyTip3' => '',
            'FiyTip4' => '',
            'FiyTip5' => '',
            'FiyTip6' => '',
            'FiyTip7' => '',
            'FiyTip8' => '',
            'FiyTip9' => '',
            'FiyTip10' => '',
            'Location' => ($locations) ? implode(",",$locations) : '',
            'ParaCinsi' => '',
            'Option' => '',
            'Barcode' => $barcode ?? '',
            'aktifBarkod' => '*',
            'MagazaMevcutRezervi' => ''
        ];
        $response = $this->apiService->call("GetWebStkMevDetail",$parameters);
        $data = array_map(function($product){return ObjectHydrator::hydrate((array) $product,new StkMevDetailResponseType());},$response->data ?? []);
        return $data;
    }

    /** Verilen sku ve zaman aralığına göre stok kartlarının renk beden kırılımlarının stok hareketlerini getirir.*/
    public function getFullStkMevDetail(string $sku = null, string $barcode=null, array $locations=null, DateTime $startDate=null): array
    {

        $parameters = [
            'tar1' => ($startDate) ? $startDate->format('Y-m-d H:i:s') : '',
            'tar2' => ($startDate) ? (new DateTime())->format('Y-m-d H:i:s') : '',
            'skod1' => $sku ?? '',
            'skod2' => $sku ?? '',
            'bedgrup' =>'',
            'FiyTip1' => '',
            'FiyTip2' => '',
            'FiyTip3' => '',
            'FiyTip4' => '',
            'FiyTip5' => '',
            'FiyTip6' => '',
            'FiyTip7' => '',
            'FiyTip8' => '',
            'FiyTip9' => '',
            'FiyTip10' => '',
            'Location' => ($locations) ? implode(",",$locations) : '',
            'ParaCinsi' => '',
            'Option' => '',
            'Barcode' => $barcode ?? '',
      //      'aktifBarkod' => '*',
            'MagazaMevcutRezervi' => '',
            'TarOption' => ''
        ];
        $response = $this->apiService->call("GetWeb_Full_info_Stk_in_MevDet",$parameters);
        $data = array_map(function($product){return ObjectHydrator::hydrate((array) $product,new StkMevDetailResponseType());},$response->data ?? []);
        return $data;
    }

    /** Verilen sku, renk, numara ve lokasyona göre stok kartlarının detaylarını getirir. */
    public function getStkLocMevDetail(string $sku, ?string $color = null, ?string $sizeCode = null) : array
    {
        $parameters = [
            'skod'=>$sku ?? '',
            'xkod'=>$color ?? '',
            'ykod'=>$sizeCode ?? '',
            'Location'=> ''
        ];
        $response = $this->apiService->call("GetWebStkLocMevDetail",$parameters);
        $data = array_map(function($product){return ObjectHydrator::hydrate((array) $product,new StkLocMevDetailResponseType());},$response->data ?? []);
        return $data;
    }
}