<?php
namespace App\Service;
use App\Repository\CampaignItemRepository;
use Doctrine\ORM\EntityManagerInterface;

class CampaignItemService
{
    public function __construct(
        private  readonly  CampaignItemRepository $repository,
        private readonly  EntityManagerInterface  $em)
    {}


    public function allWithPaginate(array $filters = [], int $page = 1, $limit = 10): array
    {

        return $this->repository->allWithPaginate($page, $limit, $filters);
    }
    public function insert(PromoCampaignPlatform $promoDiscountPlatform)
    {
        $this->em->persist($promoDiscountPlatform);
        $this->em->flush();
    }

}