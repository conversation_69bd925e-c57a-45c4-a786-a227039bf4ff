<?php
namespace App\Service;
use Wb3\SharedBundle\Entity\Product;

class ProductDecoratorService {
    public function decorateSimple(Product $product): array
    {
        return [
            'id' => $product->getId(),
            'title' => $product->getTitle(),
            'skuColor' => $product->getSkuColor(),
            'image'=> $product->getImages()[0]?->getUrl(),
            'text' => (string) $product,
        ];
    }
}
